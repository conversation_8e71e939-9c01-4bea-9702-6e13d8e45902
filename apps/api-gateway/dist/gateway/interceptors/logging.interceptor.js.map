{"version": 3, "file": "logging.interceptor.js", "sourceRoot": "", "sources": ["../../../src/gateway/interceptors/logging.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAMwB;AAExB,8CAAiD;AAI1C,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IACZ,MAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IAE9D,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAsB,CAAC;QACxE,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QAEtD,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;QAC1D,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAClD,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC;QAG1D,MAAM,IAAI,GAAI,OAAe,CAAC,IAAI,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,WAAW,CAAC;QAE3C,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,GAAG,EAAE;YACP,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;YAEvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,aAAa;gBACb,MAAM;gBACN,GAAG;gBACH,UAAU;gBACV,QAAQ,EAAE,GAAG,QAAQ,IAAI;gBACzB,MAAM;gBACN,EAAE;gBACF,SAAS;gBACT,IAAI,EAAE,mBAAmB;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC,EACF,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;YACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC;YAEvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,aAAa;gBACb,MAAM;gBACN,GAAG;gBACH,UAAU;gBACV,QAAQ,EAAE,GAAG,QAAQ,IAAI;gBACzB,MAAM;gBACN,EAAE;gBACF,SAAS;gBACT,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AAxDY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;GACA,kBAAkB,CAwD9B"}