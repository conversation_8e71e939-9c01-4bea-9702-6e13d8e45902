"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomRateLimitGuard = void 0;
const common_1 = require("@nestjs/common");
const throttler_1 = require("@nestjs/throttler");
const throttler_2 = require("@nestjs/throttler");
const core_1 = require("@nestjs/core");
const config_1 = require("@nestjs/config");
let CustomRateLimitGuard = class CustomRateLimitGuard extends throttler_1.ThrottlerGuard {
    configService;
    constructor(options, storageService, reflector, configService) {
        super(options, storageService, reflector);
        this.configService = configService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const anonymousLimit = this.configService.get('RATE_LIMIT_ANONYMOUS', 100);
        const authenticatedLimit = this.configService.get('RATE_LIMIT_AUTHENTICATED', 1000);
        const ttl = this.configService.get('RATE_LIMIT_TTL', 3600);
        let limit;
        let key;
        if (user) {
            if (user.roles?.includes('admin')) {
                return true;
            }
            limit = authenticatedLimit;
            key = `user:${user.userId}`;
        }
        else {
            limit = anonymousLimit;
            key = `ip:${request.ip}`;
        }
        const throttlerOptions = [
            {
                name: 'default',
                ttl: ttl * 1000,
                limit: limit,
            },
        ];
        this.options = throttlerOptions;
        try {
            return await super.canActivate(context);
        }
        catch (error) {
            throw new common_1.HttpException({
                statusCode: common_1.HttpStatus.TOO_MANY_REQUESTS,
                message: `Rate limit exceeded. Limit: ${limit} requests per hour`,
                error: 'Too Many Requests',
                timestamp: new Date().toISOString(),
                path: request.url,
                correlationId: request.correlationId,
            }, common_1.HttpStatus.TOO_MANY_REQUESTS);
        }
    }
    async getTracker(req) {
        const user = req.user;
        if (user) {
            return `user:${user.userId}`;
        }
        return `ip:${req.ip}`;
    }
};
exports.CustomRateLimitGuard = CustomRateLimitGuard;
exports.CustomRateLimitGuard = CustomRateLimitGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object, Object, core_1.Reflector,
        config_1.ConfigService])
], CustomRateLimitGuard);
//# sourceMappingURL=rate-limit.guard.js.map