{"version": 3, "file": "rate-limit.guard.js", "sourceRoot": "", "sources": ["../../../src/gateway/guards/rate-limit.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAMwB;AACxB,iDAA2E;AAC3E,iDAAqD;AACrD,uCAAyC;AACzC,2CAA+C;AAGxC,IAAM,oBAAoB,GAA1B,MAAM,oBAAqB,SAAQ,0BAAc;IAK5C;IAJV,YACE,OAA+B,EAC/B,cAAgC,EAChC,SAAoB,EACZ,aAA4B;QAEpC,KAAK,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;QAFlC,kBAAa,GAAb,aAAa,CAAe;IAGtC,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAG1B,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,sBAAsB,EAAE,GAAG,CAAC,CAAC;QACnF,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,0BAA0B,EAAE,IAAI,CAAC,CAAC;QAC5F,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAGnE,IAAI,KAAa,CAAC;QAClB,IAAI,GAAW,CAAC;QAEhB,IAAI,IAAI,EAAE,CAAC;YAET,IAAI,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,KAAK,GAAG,kBAAkB,CAAC;YAC3B,GAAG,GAAG,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;QAC9B,CAAC;aAAM,CAAC;YAEN,KAAK,GAAG,cAAc,CAAC;YACvB,GAAG,GAAG,MAAM,OAAO,CAAC,EAAE,EAAE,CAAC;QAC3B,CAAC;QAGD,MAAM,gBAAgB,GAAG;YACvB;gBACE,IAAI,EAAE,SAAS;gBACf,GAAG,EAAE,GAAG,GAAG,IAAI;gBACf,KAAK,EAAE,KAAK;aACb;SACF,CAAC;QAGD,IAAY,CAAC,OAAO,GAAG,gBAAgB,CAAC;QAEzC,IAAI,CAAC;YACH,OAAO,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB;gBACE,UAAU,EAAE,mBAAU,CAAC,iBAAiB;gBACxC,OAAO,EAAE,+BAA+B,KAAK,oBAAoB;gBACjE,KAAK,EAAE,mBAAmB;gBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,IAAI,EAAE,OAAO,CAAC,GAAG;gBACjB,aAAa,EAAE,OAAO,CAAC,aAAa;aACrC,EACD,mBAAU,CAAC,iBAAiB,CAC7B,CAAC;QACJ,CAAC;IACH,CAAC;IAES,KAAK,CAAC,UAAU,CAAC,GAAwB;QACjD,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QAEtB,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;QAC/B,CAAC;QAED,OAAO,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC;IACxB,CAAC;CACF,CAAA;AA5EY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;qDAKE,gBAAS;QACG,sBAAa;GAL3B,oBAAoB,CA4EhC"}