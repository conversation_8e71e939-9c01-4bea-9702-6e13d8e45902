{"version": 3, "file": "jwt-auth.guard.js", "sourceRoot": "", "sources": ["../../../src/gateway/guards/jwt-auth.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,qCAAyC;AACzC,2CAA+C;AAC/C,uCAAyC;AAE5B,QAAA,aAAa,GAAG,UAAU,CAAC;AACjC,MAAM,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,MAAW,EAAE,GAAY,EAAE,UAA+B,EAAE,EAAE;IACzF,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,CAAC,cAAc,CAAC,qBAAa,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,cAAc,CAAC,qBAAa,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACtD,CAAC;AACH,CAAC,CAAC;AANW,QAAA,MAAM,UAMjB;AAGK,IAAM,YAAY,GAAlB,MAAM,YAAY;IAEb;IACA;IACA;IAHV,YACU,UAAsB,EACtB,aAA4B,EAC5B,SAAoB;QAFpB,eAAU,GAAV,UAAU,CAAY;QACtB,kBAAa,GAAb,aAAa,CAAe;QAC5B,cAAS,GAAT,SAAS,CAAW;IAC3B,CAAC;IAEJ,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAU,qBAAa,EAAE;YACxE,OAAO,CAAC,UAAU,EAAE;YACpB,OAAO,CAAC,QAAQ,EAAE;SACnB,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAE5F,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,8BAAqB,CAAC,mBAAmB,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,CAAC;YAC5D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAGrE,OAAO,CAAC,IAAI,GAAG;gBACb,MAAM,EAAE,OAAO,CAAC,GAAG;gBACnB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;aAC3B,CAAC;YAEF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,8BAAqB,CAAC,eAAe,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,OAAY;QACzC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACtE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/C,CAAC;IAEO,uBAAuB,CAAC,OAAY;QAC1C,OAAO,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC;IACvC,CAAC;CACF,CAAA;AAlDY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;qCAGW,gBAAU;QACP,sBAAa;QACjB,gBAAS;GAJnB,YAAY,CAkDxB"}