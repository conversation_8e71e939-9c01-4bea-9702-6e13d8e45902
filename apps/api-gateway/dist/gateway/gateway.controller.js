"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var GatewayController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GatewayController = void 0;
const common_1 = require("@nestjs/common");
const gateway_service_1 = require("./services/gateway.service");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const rate_limit_guard_1 = require("./guards/rate-limit.guard");
const logging_interceptor_1 = require("./interceptors/logging.interceptor");
const timeout_interceptor_1 = require("./interceptors/timeout.interceptor");
let GatewayController = GatewayController_1 = class GatewayController {
    gatewayService;
    logger = new common_1.Logger(GatewayController_1.name);
    constructor(gatewayService) {
        this.gatewayService = gatewayService;
    }
    async handleAuthRoutes(req, res) {
        return this.proxyRequest(req, res);
    }
    async handleUserRoutes(req, res) {
        return this.proxyRequest(req, res);
    }
    async handleProductRoutes(req, res) {
        return this.proxyRequest(req, res);
    }
    async handleReviewRoutes(req, res) {
        return this.proxyRequest(req, res);
    }
    async proxyRequest(req, res) {
        try {
            const { method, url, headers, query, body, correlationId, user } = req;
            const path = url.split('?')[0];
            this.logger.debug({
                message: 'Processing request',
                correlationId,
                method,
                path,
                userId: user?.userId || 'anonymous',
            });
            const response = await this.gatewayService.proxyRequest(path, method, headers, query, body, correlationId, user);
            const responseHeaders = response.headers;
            Object.keys(responseHeaders).forEach(key => {
                if (!this.isHopByHopHeader(key)) {
                    res.setHeader(key, responseHeaders[key]);
                }
            });
            res.status(response.status);
            if (responseHeaders['content-type']?.includes('application/json')) {
                res.json(response.data);
            }
            else if (responseHeaders['content-type']?.includes('text/')) {
                res.send(response.data);
            }
            else {
                res.send(response.data);
            }
        }
        catch (error) {
            this.logger.error({
                message: 'Error processing request',
                correlationId: req.correlationId,
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
    isHopByHopHeader(header) {
        const hopByHopHeaders = [
            'connection',
            'keep-alive',
            'proxy-authenticate',
            'proxy-authorization',
            'te',
            'trailers',
            'transfer-encoding',
            'upgrade',
        ];
        return hopByHopHeaders.includes(header.toLowerCase());
    }
};
exports.GatewayController = GatewayController;
__decorate([
    (0, common_1.All)('auth/*'),
    (0, jwt_auth_guard_1.Public)(),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GatewayController.prototype, "handleAuthRoutes", null);
__decorate([
    (0, common_1.All)('users/*'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GatewayController.prototype, "handleUserRoutes", null);
__decorate([
    (0, common_1.All)('products/*'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GatewayController.prototype, "handleProductRoutes", null);
__decorate([
    (0, common_1.All)('reviews/*'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GatewayController.prototype, "handleReviewRoutes", null);
exports.GatewayController = GatewayController = GatewayController_1 = __decorate([
    (0, common_1.Controller)('api'),
    (0, common_1.UseGuards)(rate_limit_guard_1.CustomRateLimitGuard),
    (0, common_1.UseInterceptors)(logging_interceptor_1.LoggingInterceptor, timeout_interceptor_1.TimeoutInterceptor),
    __metadata("design:paramtypes", [gateway_service_1.GatewayService])
], GatewayController);
//# sourceMappingURL=gateway.controller.js.map