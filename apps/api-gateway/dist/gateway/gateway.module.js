"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GatewayModule = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const throttler_1 = require("@nestjs/throttler");
const gateway_controller_1 = require("./gateway.controller");
const gateway_service_1 = require("./services/gateway.service");
const service_registry_service_1 = require("./services/service-registry.service");
const correlation_id_middleware_1 = require("./middleware/correlation-id.middleware");
const logging_interceptor_1 = require("./interceptors/logging.interceptor");
const timeout_interceptor_1 = require("./interceptors/timeout.interceptor");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const rate_limit_guard_1 = require("./guards/rate-limit.guard");
let GatewayModule = class GatewayModule {
    configure(consumer) {
        consumer.apply(correlation_id_middleware_1.CorrelationIdMiddleware).forRoutes('*');
    }
};
exports.GatewayModule = GatewayModule;
exports.GatewayModule = GatewayModule = __decorate([
    (0, common_1.Module)({
        imports: [
            axios_1.HttpModule.register({
                timeout: 5000,
                maxRedirects: 5,
            }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: '1h',
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            throttler_1.ThrottlerModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => [
                    {
                        name: 'default',
                        ttl: configService.get('RATE_LIMIT_TTL', 3600) * 1000,
                        limit: configService.get('RATE_LIMIT_AUTHENTICATED', 1000),
                    },
                ],
                inject: [config_1.ConfigService],
            }),
        ],
        controllers: [gateway_controller_1.GatewayController],
        providers: [
            gateway_service_1.GatewayService,
            service_registry_service_1.ServiceRegistryService,
            logging_interceptor_1.LoggingInterceptor,
            timeout_interceptor_1.TimeoutInterceptor,
            jwt_auth_guard_1.JwtAuthGuard,
            rate_limit_guard_1.CustomRateLimitGuard,
        ],
        exports: [service_registry_service_1.ServiceRegistryService],
    })
], GatewayModule);
//# sourceMappingURL=gateway.module.js.map