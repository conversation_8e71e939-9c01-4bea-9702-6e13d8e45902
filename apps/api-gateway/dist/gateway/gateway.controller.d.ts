import { Response } from 'express';
import { GatewayService } from './services/gateway.service';
import { RequestWithContext } from './middleware/correlation-id.middleware';
interface RequestWithUser extends RequestWithContext {
    user?: {
        userId: string;
        email: string;
        name: string;
        roles?: string[];
    };
}
export declare class GatewayController {
    private gatewayService;
    private readonly logger;
    constructor(gatewayService: GatewayService);
    handleAuthRoutes(req: RequestWithUser, res: Response): Promise<void>;
    handleUserRoutes(req: RequestWithUser, res: Response): Promise<void>;
    handleProductRoutes(req: RequestWithUser, res: Response): Promise<void>;
    handleReviewRoutes(req: RequestWithUser, res: Response): Promise<void>;
    private proxyRequest;
    private isHopByHopHeader;
}
export {};
