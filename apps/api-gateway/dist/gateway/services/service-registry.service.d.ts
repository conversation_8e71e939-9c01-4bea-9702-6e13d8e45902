import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { ServiceConfig } from '../dto/proxy-request.dto';
export declare class ServiceRegistryService {
    private configService;
    private httpService;
    private readonly logger;
    private readonly services;
    constructor(configService: ConfigService, httpService: HttpService);
    private initializeServices;
    getServiceByRoute(path: string): ServiceConfig | null;
    getAllServices(): ServiceConfig[];
    getService(name: string): ServiceConfig | null;
    checkServiceHealth(serviceName: string): Promise<boolean>;
    checkAllServicesHealth(): Promise<Record<string, boolean>>;
}
