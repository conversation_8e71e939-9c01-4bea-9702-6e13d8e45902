import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AxiosResponse } from 'axios';
import { ServiceRegistryService } from './service-registry.service';
export declare class GatewayService {
    private httpService;
    private configService;
    private serviceRegistry;
    private readonly logger;
    constructor(httpService: HttpService, configService: ConfigService, serviceRegistry: ServiceRegistryService);
    proxyRequest(path: string, method: string, headers: Record<string, string>, query: Record<string, any>, body: any, correlationId: string, user?: any): Promise<AxiosResponse>;
    private makeRequestWithRetry;
    private shouldRetry;
    private delay;
    private prepareHeaders;
    private handleProxyError;
}
