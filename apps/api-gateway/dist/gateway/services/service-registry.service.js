"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ServiceRegistryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceRegistryService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
let ServiceRegistryService = ServiceRegistryService_1 = class ServiceRegistryService {
    configService;
    httpService;
    logger = new common_1.Logger(ServiceRegistryService_1.name);
    services = new Map();
    constructor(configService, httpService) {
        this.configService = configService;
        this.httpService = httpService;
        this.initializeServices();
    }
    initializeServices() {
        const services = [
            {
                name: 'user-service',
                url: this.configService.get('USER_SERVICE_URL', 'http://localhost:3001'),
                timeout: this.configService.get('SERVICE_TIMEOUT', 5000),
                retries: this.configService.get('MAX_RETRIES', 3),
                retryDelay: this.configService.get('RETRY_DELAY', 1000),
                healthCheck: '/health',
            },
            {
                name: 'product-service',
                url: this.configService.get('PRODUCT_SERVICE_URL', 'http://localhost:3002'),
                timeout: this.configService.get('SERVICE_TIMEOUT', 5000),
                retries: this.configService.get('MAX_RETRIES', 3),
                retryDelay: this.configService.get('RETRY_DELAY', 1000),
                healthCheck: '/health',
            },
            {
                name: 'review-service',
                url: this.configService.get('REVIEW_SERVICE_URL', 'http://localhost:3003'),
                timeout: this.configService.get('SERVICE_TIMEOUT', 5000),
                retries: this.configService.get('MAX_RETRIES', 3),
                retryDelay: this.configService.get('RETRY_DELAY', 1000),
                healthCheck: '/health',
            },
        ];
        services.forEach((service) => {
            this.services.set(service.name, service);
        });
        this.logger.log(`Initialized ${services.length} services`);
    }
    getServiceByRoute(path) {
        if (path.startsWith('/api/auth') || path.startsWith('/api/users')) {
            return this.services.get('user-service') || null;
        }
        if (path.startsWith('/api/products')) {
            return this.services.get('product-service') || null;
        }
        if (path.startsWith('/api/reviews')) {
            return this.services.get('review-service') || null;
        }
        return null;
    }
    getAllServices() {
        return Array.from(this.services.values());
    }
    getService(name) {
        return this.services.get(name) || null;
    }
    async checkServiceHealth(serviceName) {
        const service = this.services.get(serviceName);
        if (!service) {
            return false;
        }
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`${service.url}${service.healthCheck}`, {
                timeout: 3000,
            }));
            return response.status === 200;
        }
        catch (error) {
            this.logger.warn(`Health check failed for ${serviceName}: ${error.message}`);
            return false;
        }
    }
    async checkAllServicesHealth() {
        const healthStatus = {};
        const healthChecks = Array.from(this.services.keys()).map(async (serviceName) => {
            healthStatus[serviceName] = await this.checkServiceHealth(serviceName);
        });
        await Promise.all(healthChecks);
        return healthStatus;
    }
};
exports.ServiceRegistryService = ServiceRegistryService;
exports.ServiceRegistryService = ServiceRegistryService = ServiceRegistryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        axios_1.HttpService])
], ServiceRegistryService);
//# sourceMappingURL=service-registry.service.js.map