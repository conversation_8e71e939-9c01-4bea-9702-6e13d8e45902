"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GatewayService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GatewayService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_1 = require("@nestjs/config");
const rxjs_1 = require("rxjs");
const service_registry_service_1 = require("./service-registry.service");
let GatewayService = GatewayService_1 = class GatewayService {
    httpService;
    configService;
    serviceRegistry;
    logger = new common_1.Logger(GatewayService_1.name);
    constructor(httpService, configService, serviceRegistry) {
        this.httpService = httpService;
        this.configService = configService;
        this.serviceRegistry = serviceRegistry;
    }
    async proxyRequest(path, method, headers, query, body, correlationId, user) {
        const service = this.serviceRegistry.getServiceByRoute(path);
        if (!service) {
            throw new common_1.HttpException({
                statusCode: common_1.HttpStatus.NOT_FOUND,
                message: 'Service not found for this route',
                error: 'Not Found',
                timestamp: new Date().toISOString(),
                path,
                correlationId,
            }, common_1.HttpStatus.NOT_FOUND);
        }
        const servicePath = path.replace(/^\/api/, '');
        const targetUrl = `${service.url}${servicePath}`;
        const forwardHeaders = this.prepareHeaders(headers, correlationId, user);
        this.logger.log({
            message: 'Proxying request',
            correlationId,
            method,
            targetUrl,
            service: service.name,
            userId: user?.userId || 'anonymous',
        });
        return this.makeRequestWithRetry(service, targetUrl, method, forwardHeaders, query, body, correlationId);
    }
    async makeRequestWithRetry(service, url, method, headers, query, body, correlationId, attempt = 1) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.request({
                method: method.toLowerCase(),
                url,
                headers,
                params: query,
                data: body,
                timeout: service.timeout,
                maxRedirects: 5,
            }));
            this.logger.log({
                message: 'Request successful',
                correlationId,
                service: service.name,
                statusCode: response.status,
                attempt,
            });
            return response;
        }
        catch (error) {
            const axiosError = error;
            if (this.shouldRetry(axiosError, attempt, service.retries)) {
                this.logger.warn({
                    message: 'Request failed, retrying',
                    correlationId,
                    service: service.name,
                    attempt,
                    error: axiosError.message,
                });
                await this.delay(service.retryDelay * Math.pow(2, attempt - 1));
                return this.makeRequestWithRetry(service, url, method, headers, query, body, correlationId, attempt + 1);
            }
            this.handleProxyError(axiosError, service, correlationId);
        }
    }
    shouldRetry(error, attempt, maxRetries) {
        if (attempt >= maxRetries) {
            return false;
        }
        if (!error.response) {
            return true;
        }
        const status = error.response.status;
        return status >= 500 && status < 600;
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    prepareHeaders(originalHeaders, correlationId, user) {
        const headers = { ...originalHeaders };
        headers['x-correlation-id'] = correlationId;
        if (user) {
            headers['x-user-id'] = user.userId;
            headers['x-user-email'] = user.email;
            headers['x-user-roles'] = JSON.stringify(user.roles || []);
        }
        delete headers['host'];
        delete headers['connection'];
        delete headers['upgrade'];
        delete headers['proxy-authenticate'];
        delete headers['proxy-authorization'];
        delete headers['te'];
        delete headers['trailers'];
        delete headers['transfer-encoding'];
        return headers;
    }
    handleProxyError(error, service, correlationId) {
        this.logger.error({
            message: 'Proxy request failed',
            correlationId,
            service: service.name,
            error: error.message,
            status: error.response?.status,
        });
        if (!error.response) {
            throw new common_1.ServiceUnavailableException({
                statusCode: common_1.HttpStatus.SERVICE_UNAVAILABLE,
                message: `Service ${service.name} is unavailable`,
                error: 'Service Unavailable',
                timestamp: new Date().toISOString(),
                correlationId,
            });
        }
        const status = error.response.status;
        const data = error.response.data;
        throw new common_1.HttpException({
            statusCode: status,
            message: data?.message || 'Downstream service error',
            error: data?.error || 'Bad Gateway',
            timestamp: new Date().toISOString(),
            correlationId,
            service: service.name,
        }, status);
    }
};
exports.GatewayService = GatewayService;
exports.GatewayService = GatewayService = GatewayService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService,
        config_1.ConfigService,
        service_registry_service_1.ServiceRegistryService])
], GatewayService);
//# sourceMappingURL=gateway.service.js.map