import { HealthCheckService, HttpHealthIndicator, MemoryHealthIndicator } from '@nestjs/terminus';
import { ServiceRegistryService } from '../gateway/services/service-registry.service';
export declare class HealthController {
    private health;
    private http;
    private memory;
    private serviceRegistry;
    constructor(health: HealthCheckService, http: HttpHealthIndicator, memory: MemoryHealthIndicator, serviceRegistry: ServiceRegistryService);
    check(): Promise<import("@nestjs/terminus").HealthCheckResult>;
    checkServices(): Promise<{
        status: string;
        timestamp: string;
        services: Record<string, boolean>;
    }>;
    detailedHealth(): Promise<{
        status: string;
        timestamp: string;
        gateway: {
            uptime: number;
            memory: NodeJS.MemoryUsage;
            version: string;
        };
        services: {
            name: string;
            url: string;
            healthy: boolean;
            timeout: number;
            retries: number;
        }[];
    }>;
    private checkUserService;
    private checkProductService;
    private checkReviewService;
}
