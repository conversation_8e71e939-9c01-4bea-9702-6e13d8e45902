"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const service_registry_service_1 = require("../gateway/services/service-registry.service");
const jwt_auth_guard_1 = require("../gateway/guards/jwt-auth.guard");
let HealthController = class HealthController {
    health;
    http;
    memory;
    serviceRegistry;
    constructor(health, http, memory, serviceRegistry) {
        this.health = health;
        this.http = http;
        this.memory = memory;
        this.serviceRegistry = serviceRegistry;
    }
    check() {
        return this.health.check([
            () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
            () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
            () => this.checkUserService(),
            () => this.checkProductService(),
            () => this.checkReviewService(),
        ]);
    }
    async checkServices() {
        const healthStatus = await this.serviceRegistry.checkAllServicesHealth();
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            services: healthStatus,
        };
    }
    async detailedHealth() {
        const services = this.serviceRegistry.getAllServices();
        const serviceHealth = await this.serviceRegistry.checkAllServicesHealth();
        const serviceDetails = services.map((service) => ({
            name: service.name,
            url: service.url,
            healthy: serviceHealth[service.name] || false,
            timeout: service.timeout,
            retries: service.retries,
        }));
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            gateway: {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                version: process.version,
            },
            services: serviceDetails,
        };
    }
    async checkUserService() {
        const service = this.serviceRegistry.getService('user-service');
        if (!service) {
            throw new Error('User service not configured');
        }
        return this.http.pingCheck('user-service', `${service.url}/health`);
    }
    async checkProductService() {
        const service = this.serviceRegistry.getService('product-service');
        if (!service) {
            throw new Error('Product service not configured');
        }
        return this.http.pingCheck('product-service', `${service.url}/health`);
    }
    async checkReviewService() {
        const service = this.serviceRegistry.getService('review-service');
        if (!service) {
            throw new Error('Review service not configured');
        }
        return this.http.pingCheck('review-service', `${service.url}/health`);
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)(),
    (0, jwt_auth_guard_1.Public)(),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "check", null);
__decorate([
    (0, common_1.Get)('services'),
    (0, jwt_auth_guard_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "checkServices", null);
__decorate([
    (0, common_1.Get)('detailed'),
    (0, jwt_auth_guard_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "detailedHealth", null);
exports.HealthController = HealthController = __decorate([
    (0, common_1.Controller)('health'),
    __metadata("design:paramtypes", [terminus_1.HealthCheckService,
        terminus_1.HttpHealthIndicator,
        terminus_1.MemoryHealthIndicator,
        service_registry_service_1.ServiceRegistryService])
], HealthController);
//# sourceMappingURL=health.controller.js.map