"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = isULID;
var _assertString = _interopRequireDefault(require("./util/assertString"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function isULID(str) {
  (0, _assertString.default)(str);
  return /^[0-7][0-9A-HJKMNP-TV-Z]{25}$/i.test(str);
}
module.exports = exports.default;
module.exports.default = exports.default;