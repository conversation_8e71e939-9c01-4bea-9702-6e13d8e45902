import { ValidationOptions } from '../ValidationOptions';
export declare const IS_NUMBER = "isNumber";
/**
 * Options to be passed to IsNumber decorator.
 */
export interface IsNumberOptions {
    allowNaN?: boolean;
    allowInfinity?: boolean;
    maxDecimalPlaces?: number;
}
/**
 * Checks if a given value is a number.
 */
export declare function isNumber(value: unknown, options?: IsNumberOptions): value is number;
/**
 * Checks if a value is a number.
 */
export declare function IsNumber(options?: IsNumberOptions, validationOptions?: ValidationOptions): PropertyDecorator;
