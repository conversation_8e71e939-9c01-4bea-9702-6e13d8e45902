import { ValidationOptions } from '../ValidationOptions';
export declare const IS_FULL_WIDTH = "isFullWidth";
/**
 * Checks if the string contains any full-width chars.
 * If given value is not a string, then it returns false.
 */
export declare function isFullWidth(value: unknown): boolean;
/**
 * Checks if the string contains any full-width chars.
 * If given value is not a string, then it returns false.
 */
export declare function IsFullWidth(validationOptions?: ValidationOptions): PropertyDecorator;
