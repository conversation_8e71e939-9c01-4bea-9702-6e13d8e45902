import { ValidationOptions } from '../ValidationOptions';
export declare const ARRAY_MIN_SIZE = "arrayMinSize";
/**
 * Checks if the array's length is greater than or equal to the specified number.
 * If null or undefined is given then this function returns false.
 */
export declare function arrayMinSize(array: unknown, min: number): boolean;
/**
 * Checks if the array's length is greater than or equal to the specified number.
 * If null or undefined is given then this function returns false.
 */
export declare function ArrayMinSize(min: number, validationOptions?: ValidationOptions): PropertyDecorator;
