{"version": 3, "file": "IsNotEmpty.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsNotEmpty.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,YAAY,GAAG,YAAY,CAAC;AAEzC;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,KAAc;IACvC,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,iBAAqC;IAC9D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,UAAU,CAAC,KAAK,CAAC,EAAjB,CAAiB;YACrD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,+BAA+B,EAA5C,CAA4C,EAAE,iBAAiB,CAAC;SAC5G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_NOT_EMPTY = 'isNotEmpty';\n\n/**\n * Checks if given value is not empty (!== '', !== null, !== undefined).\n */\nexport function isNotEmpty(value: unknown): boolean {\n  return value !== '' && value !== null && value !== undefined;\n}\n\n/**\n * Checks if given value is not empty (!== '', !== null, !== undefined).\n */\nexport function IsNotEmpty(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_NOT_EMPTY,\n      validator: {\n        validate: (value, args): boolean => isNotEmpty(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property should not be empty', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}