# Gateway Configuration
GATEWAY_PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Service URLs
USER_SERVICE_URL=http://localhost:3001
PRODUCT_SERVICE_URL=http://localhost:3002
REVIEW_SERVICE_URL=http://localhost:3003

# Rate Limiting
RATE_LIMIT_TTL=3600
RATE_LIMIT_ANONYMOUS=100
RATE_LIMIT_AUTHENTICATED=1000

# Timeouts & Retries
SERVICE_TIMEOUT=5000
MAX_RETRIES=3
RETRY_DELAY=1000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Node Environment
NODE_ENV=development
