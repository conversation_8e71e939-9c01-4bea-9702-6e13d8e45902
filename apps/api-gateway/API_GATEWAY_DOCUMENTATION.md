# API Gateway Documentation

A comprehensive API Gateway microservice built with NestJS that serves as a centralized entry point and reverse proxy for the Product Hunt application's microservices architecture.

## Features

### 🔐 **JWT Authentication Integration**
- Seamless integration with User Service JWT authentication
- Bearer token and cookie-based authentication support
- User context forwarding to downstream services
- Role-based access control

### 🔄 **Request Routing & Proxy**
- Intelligent routing based on URL prefixes
- Header, query parameter, and body preservation
- Authentication context forwarding
- Support for all HTTP methods

### 🛡️ **Advanced Error Handling**
- Circuit breaker pattern for service unavailability
- Graceful degradation when services are down
- Standardized error response format
- Timeout handling with configurable limits
- Retry logic with exponential backoff

### 🚦 **Rate Limiting & Security**
- Tiered rate limiting based on user type
- IP-based rate limiting for authentication endpoints
- Request size limits and validation
- CORS configuration for frontend integration

### 📊 **Comprehensive Logging & Monitoring**
- Structured logging with correlation IDs
- Performance metrics collection
- Health check endpoints
- Request/response payload logging

## Architecture

### **Service Routing**
```
/api/auth/*     → User Service (port 3001)
/api/users/*    → User Service (port 3001)
/api/products/* → Product Service (port 3002)
/api/reviews/*  → Review Service (port 3003)
```

### **Rate Limiting Tiers**
- **Anonymous users**: 100 requests/hour
- **Authenticated users**: 1000 requests/hour
- **Admin users**: Unlimited

## API Endpoints

### **Health Checks**

#### Basic Health Check
- **GET** `/health`
- **Response**:
```json
{
  "status": "ok",
  "info": {
    "memory_heap": { "status": "up" },
    "memory_rss": { "status": "up" },
    "user-service": { "status": "up" },
    "product-service": { "status": "up" },
    "review-service": { "status": "up" }
  }
}
```

#### Service Health Check
- **GET** `/health/services`
- **Response**:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "user-service": true,
    "product-service": true,
    "review-service": false
  }
}
```

#### Detailed Health Check
- **GET** `/health/detailed`
- **Response**:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "gateway": {
    "uptime": 3600,
    "memory": {
      "rss": 50331648,
      "heapTotal": 20971520,
      "heapUsed": 15728640
    },
    "version": "v18.17.0"
  },
  "services": [
    {
      "name": "user-service",
      "url": "http://localhost:3001",
      "healthy": true,
      "timeout": 5000,
      "retries": 3
    }
  ]
}
```

### **Proxied Routes**

#### Authentication Routes (Public)
- **POST** `/api/auth/register`
- **POST** `/api/auth/login`
- All requests forwarded to User Service without authentication

#### User Routes (Protected)
- **GET** `/api/auth/profile`
- **PATCH** `/api/auth/profile`
- **GET** `/api/users/*`
- Requires valid JWT token

#### Product Routes (Protected)
- **GET** `/api/products`
- **POST** `/api/products`
- **GET** `/api/products/:id`
- **PUT** `/api/products/:id`
- **DELETE** `/api/products/:id`
- Requires valid JWT token

#### Review Routes (Protected)
- **GET** `/api/reviews`
- **POST** `/api/reviews`
- **GET** `/api/reviews/:id`
- **PUT** `/api/reviews/:id`
- **DELETE** `/api/reviews/:id`
- Requires valid JWT token

## Configuration

### **Environment Variables**
```env
# Gateway Configuration
GATEWAY_PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Service URLs
USER_SERVICE_URL=http://localhost:3001
PRODUCT_SERVICE_URL=http://localhost:3002
REVIEW_SERVICE_URL=http://localhost:3003

# Rate Limiting
RATE_LIMIT_TTL=3600
RATE_LIMIT_ANONYMOUS=100
RATE_LIMIT_AUTHENTICATED=1000

# Timeouts & Retries
SERVICE_TIMEOUT=5000
MAX_RETRIES=3
RETRY_DELAY=1000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
```

## Request/Response Flow

### **1. Request Processing**
1. **Correlation ID**: Generated or extracted from headers
2. **Rate Limiting**: Applied based on user type and IP
3. **Authentication**: JWT validation for protected routes
4. **Routing**: Service selection based on URL prefix
5. **Proxy**: Request forwarded to downstream service
6. **Response**: Headers and data forwarded back to client

### **2. Error Handling**
```json
{
  "statusCode": 500,
  "message": "Service user-service is unavailable",
  "error": "Service Unavailable",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/auth/login",
  "correlationId": "uuid-correlation-id"
}
```

### **3. Logging Format**
```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "correlationId": "uuid-correlation-id",
  "method": "POST",
  "url": "/api/auth/login",
  "statusCode": 200,
  "duration": "150ms",
  "userId": "user-id-or-anonymous",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0...",
  "type": "request_completed"
}
```

## Security Features

### **JWT Token Validation**
- Validates tokens using the same secret as User Service
- Extracts user context for downstream services
- Supports both Bearer tokens and cookies

### **Request Headers Forwarded**
```
x-correlation-id: uuid-correlation-id
x-user-id: user-id
x-user-email: <EMAIL>
x-user-roles: ["user", "admin"]
```

### **Rate Limiting**
- IP-based limiting for anonymous users
- User-based limiting for authenticated users
- Admin users bypass rate limiting
- Configurable limits and time windows

## Monitoring & Observability

### **Metrics Collected**
- Request count by service
- Response times by endpoint
- Error rates by service
- Rate limit violations
- Service health status

### **Correlation ID Tracking**
- Unique ID for each request
- Propagated to all downstream services
- Included in all log entries
- Returned in response headers

## Development & Testing

### **Running the Gateway**
```bash
cd apps/api-gateway
npm install
cp .env.example .env
npm run start:dev
```

### **Testing**
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

### **Health Check**
```bash
curl http://localhost:3000/health
```

## Production Considerations

### **Performance**
- Connection pooling for HTTP requests
- Request deduplication for identical concurrent requests
- Memory usage monitoring
- Graceful shutdown handling

### **Scalability**
- Horizontal scaling support
- Load balancing for multiple service instances
- Circuit breaker pattern for fault tolerance
- Configurable timeouts and retries

### **Security**
- JWT secret rotation support
- Rate limiting per service
- Request size limits
- CORS configuration
- Secure headers forwarding
