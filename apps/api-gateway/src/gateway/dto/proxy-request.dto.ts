import { IsOptional, IsString, IsObject } from 'class-validator';

export class ProxyRequestDto {
  @IsString()
  method: string;

  @IsString()
  url: string;

  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @IsOptional()
  @IsObject()
  query?: Record<string, any>;

  @IsOptional()
  body?: any;
}

export interface ErrorResponse {
  statusCode: number;
  message: string;
  error: string;
  timestamp: string;
  path: string;
  correlationId: string;
}

export interface ServiceConfig {
  name: string;
  url: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  healthCheck: string;
}

export interface UserContext {
  userId: string;
  email: string;
  name: string;
  roles?: string[];
}

export interface RequestContext {
  correlationId: string;
  user?: UserContext;
  startTime: number;
  service?: string;
}
