import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { GatewayController } from './gateway.controller';
import { GatewayService } from './services/gateway.service';
import { ServiceRegistryService } from './services/service-registry.service';
import { CorrelationIdMiddleware } from './middleware/correlation-id.middleware';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { TimeoutInterceptor } from './interceptors/timeout.interceptor';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CustomRateLimitGuard } from './guards/rate-limit.guard';

@Module({
  imports: [
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: '1h',
        },
      }),
      inject: [ConfigService],
    }),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => [
        {
          name: 'default',
          ttl: configService.get<number>('RATE_LIMIT_TTL', 3600) * 1000,
          limit: configService.get<number>('RATE_LIMIT_AUTHENTICATED', 1000),
        },
      ],
      inject: [ConfigService],
    }),
  ],
  controllers: [GatewayController],
  providers: [
    GatewayService,
    ServiceRegistryService,
    LoggingInterceptor,
    TimeoutInterceptor,
    JwtAuthGuard,
    CustomRateLimitGuard,
  ],
  exports: [ServiceRegistryService],
})
export class GatewayModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(CorrelationIdMiddleware).forRoutes('*');
  }
}
