import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { JwtService } from '@nestjs/jwt';
import { GatewayService } from './services/gateway.service';
import { ServiceRegistryService } from './services/service-registry.service';
import { of } from 'rxjs';

describe('Gateway Services', () => {
  let gatewayService: GatewayService;
  let serviceRegistry: ServiceRegistryService;

  const mockHttpService = {
    request: jest.fn(),
    get: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        JWT_SECRET: 'test-secret',
        USER_SERVICE_URL: 'http://localhost:3001',
        PRODUCT_SERVICE_URL: 'http://localhost:3002',
        REVIEW_SERVICE_URL: 'http://localhost:3003',
        SERVICE_TIMEOUT: 5000,
        MAX_RETRIES: 3,
        RETRY_DELAY: 1000,
        RATE_LIMIT_TTL: 3600,
        RATE_LIMIT_ANONYMOUS: 100,
        RATE_LIMIT_AUTHENTICATED: 1000,
      };
      return config[key] || defaultValue;
    }),
  };

  const mockJwtService = {
    verifyAsync: jest.fn(),
    sign: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GatewayService,
        ServiceRegistryService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    gatewayService = module.get<GatewayService>(GatewayService);
    serviceRegistry = module.get<ServiceRegistryService>(ServiceRegistryService);
  });

  it('should be defined', () => {
    expect(gatewayService).toBeDefined();
    expect(serviceRegistry).toBeDefined();
  });

  describe('ServiceRegistryService', () => {
    it('should route auth requests to user service', () => {
      const service = serviceRegistry.getServiceByRoute('/api/auth/login');
      expect(service?.name).toBe('user-service');
    });

    it('should route user requests to user service', () => {
      const service = serviceRegistry.getServiceByRoute('/api/users/profile');
      expect(service?.name).toBe('user-service');
    });

    it('should route product requests to product service', () => {
      const service = serviceRegistry.getServiceByRoute('/api/products/123');
      expect(service?.name).toBe('product-service');
    });

    it('should route review requests to review service', () => {
      const service = serviceRegistry.getServiceByRoute('/api/reviews/456');
      expect(service?.name).toBe('review-service');
    });

    it('should return null for unknown routes', () => {
      const service = serviceRegistry.getServiceByRoute('/api/unknown/route');
      expect(service).toBeNull();
    });
  });

  describe('GatewayService', () => {
    it('should make successful proxy request', async () => {
      const mockAxiosResponse = {
        status: 200,
        headers: { 'content-type': 'application/json' },
        data: { message: 'success' },
      };

      mockHttpService.request.mockReturnValue(of(mockAxiosResponse));

      const result = await gatewayService.proxyRequest(
        '/api/auth/login',
        'POST',
        { 'content-type': 'application/json' },
        {},
        { email: '<EMAIL>' },
        'test-correlation-id',
      );

      expect(result).toEqual(mockAxiosResponse);
      expect(mockHttpService.request).toHaveBeenCalled();
    });
  });
});
