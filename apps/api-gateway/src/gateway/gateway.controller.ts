import {
  Controller,
  All,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { GatewayService } from './services/gateway.service';
import { JwtAuthGuard, Public } from './guards/jwt-auth.guard';
import { CustomRateLimitGuard } from './guards/rate-limit.guard';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { TimeoutInterceptor } from './interceptors/timeout.interceptor';
import { RequestWithContext } from './middleware/correlation-id.middleware';

interface RequestWithUser extends RequestWithContext {
  user?: {
    userId: string;
    email: string;
    name: string;
    roles?: string[];
  };
}

@Controller('api')
@UseGuards(CustomRateLimitGuard)
@UseInterceptors(LoggingInterceptor, TimeoutInterceptor)
export class GatewayController {
  private readonly logger = new Logger(GatewayController.name);

  constructor(private gatewayService: GatewayService) {}

  @All('auth/*')
  @Public()
  async handleAuthRoutes(
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    return this.proxyRequest(req, res);
  }

  @All('users/*')
  @UseGuards(JwtAuthGuard)
  async handleUserRoutes(
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    return this.proxyRequest(req, res);
  }

  @All('products/*')
  @UseGuards(JwtAuthGuard)
  async handleProductRoutes(
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    return this.proxyRequest(req, res);
  }

  @All('reviews/*')
  @UseGuards(JwtAuthGuard)
  async handleReviewRoutes(
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    return this.proxyRequest(req, res);
  }

  private async proxyRequest(req: RequestWithUser, res: Response) {
    try {
      const { method, url, headers, query, body, correlationId, user } = req;
      
      // Extract the path from the URL
      const path = url.split('?')[0];

      this.logger.debug({
        message: 'Processing request',
        correlationId,
        method,
        path,
        userId: user?.userId || 'anonymous',
      });

      const response = await this.gatewayService.proxyRequest(
        path,
        method,
        headers as Record<string, string>,
        query as Record<string, any>,
        body,
        correlationId,
        user,
      );

      // Forward response headers (excluding hop-by-hop headers)
      const responseHeaders = response.headers;
      Object.keys(responseHeaders).forEach(key => {
        if (!this.isHopByHopHeader(key)) {
          res.setHeader(key, responseHeaders[key]);
        }
      });

      // Set status and send response
      res.status(response.status);
      
      // Handle different content types
      if (responseHeaders['content-type']?.includes('application/json')) {
        res.json(response.data);
      } else if (responseHeaders['content-type']?.includes('text/')) {
        res.send(response.data);
      } else {
        res.send(response.data);
      }
    } catch (error) {
      this.logger.error({
        message: 'Error processing request',
        correlationId: req.correlationId,
        error: error.message,
        stack: error.stack,
      });

      // If it's already an HTTP exception, let NestJS handle it
      throw error;
    }
  }

  private isHopByHopHeader(header: string): boolean {
    const hopByHopHeaders = [
      'connection',
      'keep-alive',
      'proxy-authenticate',
      'proxy-authorization',
      'te',
      'trailers',
      'transfer-encoding',
      'upgrade',
    ];
    
    return hopByHopHeaders.includes(header.toLowerCase());
  }
}
