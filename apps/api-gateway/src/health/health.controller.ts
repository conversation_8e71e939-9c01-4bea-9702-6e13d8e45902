import { Controller, Get } from '@nestjs/common';
import {
  HealthCheckService,
  HealthCheck,
  HttpHealthIndicator,
  MemoryHealthIndicator,
} from '@nestjs/terminus';
import { ServiceRegistryService } from '../gateway/services/service-registry.service';
import { Public } from '../gateway/guards/jwt-auth.guard';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private memory: MemoryHealthIndicator,
    private serviceRegistry: ServiceRegistryService,
  ) {}

  @Get()
  @Public()
  @HealthCheck()
  check() {
    return this.health.check([
      // Memory check
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),

      // Service health checks
      () => this.checkUserService(),
      () => this.checkProductService(),
      () => this.checkReviewService(),
    ]);
  }

  @Get('services')
  @Public()
  async checkServices() {
    const healthStatus = await this.serviceRegistry.checkAllServicesHealth();

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: healthStatus,
    };
  }

  @Get('detailed')
  @Public()
  async detailedHealth() {
    const services = this.serviceRegistry.getAllServices();
    const serviceHealth = await this.serviceRegistry.checkAllServicesHealth();

    const serviceDetails = services.map((service) => ({
      name: service.name,
      url: service.url,
      healthy: serviceHealth[service.name] || false,
      timeout: service.timeout,
      retries: service.retries,
    }));

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      gateway: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version,
      },
      services: serviceDetails,
    };
  }

  private async checkUserService() {
    const service = this.serviceRegistry.getService('user-service');
    if (!service) {
      throw new Error('User service not configured');
    }

    return this.http.pingCheck('user-service', `${service.url}/health`);
  }

  private async checkProductService() {
    const service = this.serviceRegistry.getService('product-service');
    if (!service) {
      throw new Error('Product service not configured');
    }

    return this.http.pingCheck('product-service', `${service.url}/health`);
  }

  private async checkReviewService() {
    const service = this.serviceRegistry.getService('review-service');
    if (!service) {
      throw new Error('Review service not configured');
    }

    return this.http.pingCheck('review-service', `${service.url}/health`);
  }
}
