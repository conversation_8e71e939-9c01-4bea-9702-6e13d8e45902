import { AuthService, AuthResponse } from './auth.service';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { LoginUserDto } from './dto/login-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class AuthController {
    private authService;
    private userService;
    constructor(authService: AuthService, userService: UserService);
    register(createUserDto: CreateUserDto): Promise<{
        statusCode: number;
        message: string;
        data: AuthResponse;
    }>;
    login(loginUserDto: LoginUserDto): Promise<{
        statusCode: number;
        message: string;
        data: AuthResponse;
    }>;
    getProfile(req: any): Promise<{
        statusCode: number;
        message: string;
        data: {
            id: string;
            email: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
        };
    }>;
    updateProfile(req: any, updateUserDto: UpdateUserDto): Promise<{
        statusCode: number;
        message: string;
        data: {
            id: string;
            email: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
        };
    }>;
}
