# User Authentication Service API Documentation

A comprehensive User Authentication and Management Service built with NestJS, MongoDB, and JWT authentication.

## Features

- **User Registration**: Secure user registration with email validation
- **User Authentication**: JWT-based login system
- **Profile Management**: View and update user profiles
- **Password Security**: Bcrypt password hashing
- **Input Validation**: Comprehensive DTO validation
- **MongoDB Integration**: Mongoose ODM for data persistence
- **Error Handling**: Proper HTTP status codes and error messages

## Tech Stack

- **Framework**: NestJS
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: class-validator & class-transformer
- **Password Hashing**: bcryptjs
- **Testing**: Jest

## Environment Setup

1. Copy the environment example file:
```bash
$ cp .env.example .env
```

2. Update the environment variables in `.env`:
```env
MONGODB_URI=mongodb://localhost:27017/product-hunt-users
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=1h
PORT=3001
NODE_ENV=development
```

## API Endpoints

### Authentication

#### Register User
- **POST** `/auth/register`
- **Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "John Doe"
}
```
- **Response**:
```json
{
  "statusCode": 201,
  "message": "User registered successfully",
  "data": {
    "access_token": "jwt-token-here",
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "John Doe",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

#### Login User
- **POST** `/auth/login`
- **Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
- **Response**:
```json
{
  "statusCode": 200,
  "message": "Login successful",
  "data": {
    "access_token": "jwt-token-here",
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "John Doe",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

#### Get Profile (Protected)
- **GET** `/auth/profile`
- **Headers**: `Authorization: Bearer <jwt-token>`
- **Response**:
```json
{
  "statusCode": 200,
  "message": "Profile retrieved successfully",
  "data": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "John Doe",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Update Profile (Protected)
- **PATCH** `/auth/profile`
- **Headers**: `Authorization: Bearer <jwt-token>`
- **Body**:
```json
{
  "name": "Jane Doe",
  "email": "<EMAIL>"
}
```
- **Response**:
```json
{
  "statusCode": 200,
  "message": "Profile updated successfully",
  "data": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "Jane Doe",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## Validation Rules

### Registration
- **Email**: Must be a valid email format, required, unique
- **Password**: Minimum 6 characters, required
- **Name**: Minimum 2 characters, required

### Login
- **Email**: Must be a valid email format, required
- **Password**: Required

### Profile Update
- **Email**: Must be a valid email format, optional, unique
- **Name**: Minimum 2 characters, optional

## Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": ["Validation error messages"],
  "error": "Bad Request"
}
```

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Invalid email or password",
  "error": "Unauthorized"
}
```

### 409 Conflict
```json
{
  "statusCode": 409,
  "message": "Email already exists",
  "error": "Conflict"
}
```

## Security Features

- **Password Hashing**: All passwords are hashed using bcrypt with salt rounds of 12
- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive validation using class-validator
- **Email Uniqueness**: Prevents duplicate email registrations
- **Protected Routes**: JWT guard protection for sensitive endpoints

## Project Structure

```
src/
├── auth/
│   ├── dto/
│   │   ├── create-user.dto.ts
│   │   ├── login-user.dto.ts
│   │   └── update-user.dto.ts
│   ├── guards/
│   │   └── jwt-auth.guard.ts
│   ├── schemas/
│   │   └── user.schema.ts
│   ├── strategies/
│   │   └── jwt.strategy.ts
│   ├── auth.controller.ts
│   ├── auth.service.ts
│   ├── auth.module.ts
│   └── user.service.ts
├── app.controller.ts
├── app.module.ts
├── app.service.ts
└── main.ts
```
