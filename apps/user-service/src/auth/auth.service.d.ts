import { JwtService } from '@nestjs/jwt';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { LoginUserDto } from './dto/login-user.dto';
import { UserDocument } from './schemas/user.schema';
export interface JwtPayload {
    sub: string;
    email: string;
    name: string;
}
export interface AuthResponse {
    access_token: string;
    user: {
        id: string;
        email: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
    };
}
export declare class AuthService {
    private userService;
    private jwtService;
    constructor(userService: UserService, jwtService: JwtService);
    register(createUserDto: CreateUserDto): Promise<AuthResponse>;
    login(loginUserDto: LoginUserDto): Promise<AuthResponse>;
    validateUser(email: string, password: string): Promise<UserDocument | null>;
    hashPassword(password: string): Promise<string>;
    comparePasswords(plaintext: string, hashed: string): Promise<boolean>;
    validateUserById(userId: string): Promise<UserDocument | null>;
}
//# sourceMappingURL=auth.service.d.ts.map