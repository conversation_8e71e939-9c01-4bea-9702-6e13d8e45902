"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const mongoose_1 = require("@nestjs/mongoose");
const jwt_1 = require("@nestjs/jwt");
const common_1 = require("@nestjs/common");
const auth_controller_1 = require("./auth.controller");
const auth_service_1 = require("./auth.service");
const user_service_1 = require("./user.service");
const user_schema_1 = require("./schemas/user.schema");
describe('AuthController', () => {
    let controller;
    let authService;
    let userService;
    const mockUser = {
        _id: '507f1f77bcf86cd799439011',
        email: '<EMAIL>',
        name: 'Test User',
        password: 'hashedpassword',
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn(),
        toJSON: jest.fn(),
    };
    const mockUserModel = {
        new: jest.fn().mockResolvedValue(mockUser),
        constructor: jest.fn().mockResolvedValue(mockUser),
        find: jest.fn(),
        findOne: jest.fn(),
        findById: jest.fn(),
        findByIdAndUpdate: jest.fn(),
        findByIdAndDelete: jest.fn(),
        exec: jest.fn(),
    };
    const mockJwtService = {
        sign: jest.fn().mockReturnValue('mock-jwt-token'),
        verify: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [auth_controller_1.AuthController],
            providers: [
                auth_service_1.AuthService,
                user_service_1.UserService,
                {
                    provide: (0, mongoose_1.getModelToken)(user_schema_1.User.name),
                    useValue: mockUserModel,
                },
                {
                    provide: jwt_1.JwtService,
                    useValue: mockJwtService,
                },
            ],
        }).compile();
        controller = module.get(auth_controller_1.AuthController);
        authService = module.get(auth_service_1.AuthService);
        userService = module.get(user_service_1.UserService);
    });
    it('should be defined', () => {
        expect(controller).toBeDefined();
    });
    describe('register', () => {
        it('should register a new user successfully', async () => {
            const createUserDto = {
                email: '<EMAIL>',
                password: 'password123',
                name: 'Test User',
            };
            jest.spyOn(userService, 'findByEmail').mockResolvedValue(null);
            jest.spyOn(userService, 'create').mockResolvedValue(mockUser);
            const result = await controller.register(createUserDto);
            expect(result.statusCode).toBe(201);
            expect(result.message).toBe('User registered successfully');
            expect(result.data.access_token).toBe('mock-jwt-token');
            expect(result.data.user.email).toBe(createUserDto.email);
        });
        it('should throw ConflictException if user already exists', async () => {
            const createUserDto = {
                email: '<EMAIL>',
                password: 'password123',
                name: 'Test User',
            };
            jest.spyOn(userService, 'findByEmail').mockResolvedValue(mockUser);
            await expect(controller.register(createUserDto)).rejects.toThrow(common_1.ConflictException);
        });
    });
    describe('login', () => {
        it('should login user successfully', async () => {
            const loginUserDto = {
                email: '<EMAIL>',
                password: 'password123',
            };
            jest.spyOn(authService, 'validateUser').mockResolvedValue(mockUser);
            const result = await controller.login(loginUserDto);
            expect(result.statusCode).toBe(200);
            expect(result.message).toBe('Login successful');
            expect(result.data.access_token).toBe('mock-jwt-token');
        });
        it('should throw UnauthorizedException for invalid credentials', async () => {
            const loginUserDto = {
                email: '<EMAIL>',
                password: 'wrongpassword',
            };
            jest.spyOn(authService, 'validateUser').mockResolvedValue(null);
            await expect(controller.login(loginUserDto)).rejects.toThrow(common_1.UnauthorizedException);
        });
    });
    describe('getProfile', () => {
        it('should return user profile', async () => {
            const req = { user: mockUser };
            const result = await controller.getProfile(req);
            expect(result.statusCode).toBe(200);
            expect(result.message).toBe('Profile retrieved successfully');
            expect(result.data.email).toBe(mockUser.email);
        });
    });
    describe('updateProfile', () => {
        it('should update user profile successfully', async () => {
            const updateUserDto = { name: 'Updated Name' };
            const req = { user: mockUser };
            const updatedUser = { ...mockUser, name: 'Updated Name' };
            jest.spyOn(userService, 'updateProfile').mockResolvedValue(updatedUser);
            const result = await controller.updateProfile(req, updateUserDto);
            expect(result.statusCode).toBe(200);
            expect(result.message).toBe('Profile updated successfully');
            expect(result.data.name).toBe('Updated Name');
        });
    });
});
//# sourceMappingURL=auth.controller.spec.js.map