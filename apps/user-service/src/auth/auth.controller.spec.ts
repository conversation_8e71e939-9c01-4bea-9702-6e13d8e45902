import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { JwtService } from '@nestjs/jwt';
import { ConflictException, UnauthorizedException } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { UserService } from './user.service';
import { User } from './schemas/user.schema';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;
  let userService: UserService;

  const mockUser = {
    _id: '507f1f77bcf86cd799439011',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
    save: jest.fn(),
    toJSON: jest.fn(),
  };

  const mockUserModel = {
    new: jest.fn().mockResolvedValue(mockUser),
    constructor: jest.fn().mockResolvedValue(mockUser),
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    exec: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn().mockReturnValue('mock-jwt-token'),
    verify: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        AuthService,
        UserService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
    userService = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      const createUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      jest.spyOn(userService, 'findByEmail').mockResolvedValue(null);
      jest.spyOn(userService, 'create').mockResolvedValue(mockUser as any);

      const result = await controller.register(createUserDto);

      expect(result.statusCode).toBe(201);
      expect(result.message).toBe('User registered successfully');
      expect(result.data.access_token).toBe('mock-jwt-token');
      expect(result.data.user.email).toBe(createUserDto.email);
    });

    it('should throw ConflictException if user already exists', async () => {
      const createUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      jest.spyOn(userService, 'findByEmail').mockResolvedValue(mockUser as any);

      await expect(controller.register(createUserDto)).rejects.toThrow(ConflictException);
    });
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      const loginUserDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      jest.spyOn(authService, 'validateUser').mockResolvedValue(mockUser as any);

      const result = await controller.login(loginUserDto);

      expect(result.statusCode).toBe(200);
      expect(result.message).toBe('Login successful');
      expect(result.data.access_token).toBe('mock-jwt-token');
    });

    it('should throw UnauthorizedException for invalid credentials', async () => {
      const loginUserDto = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      jest.spyOn(authService, 'validateUser').mockResolvedValue(null);

      await expect(controller.login(loginUserDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('getProfile', () => {
    it('should return user profile', async () => {
      const req = { user: mockUser };

      const result = await controller.getProfile(req);

      expect(result.statusCode).toBe(200);
      expect(result.message).toBe('Profile retrieved successfully');
      expect(result.data.email).toBe(mockUser.email);
    });
  });

  describe('updateProfile', () => {
    it('should update user profile successfully', async () => {
      const updateUserDto = { name: 'Updated Name' };
      const req = { user: mockUser };
      const updatedUser = { ...mockUser, name: 'Updated Name' };

      jest.spyOn(userService, 'updateProfile').mockResolvedValue(updatedUser as any);

      const result = await controller.updateProfile(req, updateUserDto);

      expect(result.statusCode).toBe(200);
      expect(result.message).toBe('Profile updated successfully');
      expect(result.data.name).toBe('Updated Name');
    });
  });
});
