import { Model } from 'mongoose';
import { UserDocument } from './schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class UserService {
    private userModel;
    constructor(userModel: Model<UserDocument>);
    create(createUserDto: CreateUserDto): Promise<UserDocument>;
    findByEmail(email: string): Promise<UserDocument | null>;
    findById(id: string): Promise<UserDocument | null>;
    updateProfile(id: string, updateUserDto: UpdateUserDto): Promise<UserDocument>;
    findAll(): Promise<UserDocument[]>;
    deleteUser(id: string): Promise<void>;
}
//# sourceMappingURL=user.service.d.ts.map