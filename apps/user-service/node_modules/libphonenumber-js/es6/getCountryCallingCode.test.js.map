{"version": 3, "file": "getCountryCallingCode.test.js", "names": ["metadata", "type", "getCountryCallingCode", "describe", "it", "should", "equal", "expect", "to"], "sources": ["../source/getCountryCallingCode.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\nimport getCountryCallingCode from './getCountryCallingCode.js'\r\n\r\ndescribe('getCountryCallingCode', () => {\r\n\tit('should get country calling code', () => {\r\n\t\tgetCountryCallingCode('US', metadata).should.equal('1')\r\n\t})\r\n\r\n\tit('should throw if country is unknown', () => {\r\n\t\texpect(() => getCountryCallingCode('ZZ', metadata)).to.throw('Unknown country: ZZ')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AAEA,OAAOC,qBAAP,MAAkC,4BAAlC;AAEAC,QAAQ,CAAC,uBAAD,EAA0B,YAAM;EACvCC,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3CF,qBAAqB,CAAC,IAAD,EAAOF,QAAP,CAArB,CAAsCK,MAAtC,CAA6CC,KAA7C,CAAmD,GAAnD;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,oCAAD,EAAuC,YAAM;IAC9CG,MAAM,CAAC;MAAA,OAAML,qBAAqB,CAAC,IAAD,EAAOF,QAAP,CAA3B;IAAA,CAAD,CAAN,CAAoDQ,EAApD,UAA6D,qBAA7D;EACA,CAFC,CAAF;AAGA,CARO,CAAR"}