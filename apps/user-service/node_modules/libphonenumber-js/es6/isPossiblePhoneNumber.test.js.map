{"version": 3, "file": "isPossiblePhoneNumber.test.js", "names": ["_isPossiblePhoneNumber", "metadata", "type", "oldMetadata", "isPossiblePhoneNumber", "parameters", "push", "apply", "describe", "it", "should", "equal", "defaultCountry", "expect", "to"], "sources": ["../source/isPossiblePhoneNumber.test.js"], "sourcesContent": ["import _isPossiblePhoneNumber from './isPossiblePhoneNumber.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport oldMetadata from '../test/metadata/1.0.0/metadata.min.json' assert { type: 'json' }\r\n\r\nfunction isPossiblePhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isPossiblePhoneNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isPossiblePhoneNumber', () => {\r\n\tit('should detect whether a phone number is possible', () => {\r\n\t\tisPossiblePhoneNumber('8 (800) 555 35 35', 'RU').should.equal(true)\r\n\t\tisPossiblePhoneNumber('8 (800) 555 35 35 0', 'RU').should.equal(false)\r\n\t\tisPossiblePhoneNumber('Call: 8 (800) 555 35 35', 'RU').should.equal(false)\r\n\t\tisPossiblePhoneNumber('8 (800) 555 35 35', { defaultCountry: 'RU' }).should.equal(true)\r\n\t\tisPossiblePhoneNumber('+7 (800) 555 35 35').should.equal(true)\r\n\t\tisPossiblePhoneNumber('**** (800) 555 35 35').should.equal(false)\r\n\t\tisPossiblePhoneNumber(' +7 (800) 555 35 35').should.equal(false)\r\n\t\tisPossiblePhoneNumber(' ').should.equal(false)\r\n\t})\r\n\r\n\tit('should detect whether a phone number is possible when using old metadata', () => {\r\n\t\texpect(() => _isPossiblePhoneNumber('8 (800) 555 35 35', 'RU', oldMetadata))\r\n\t\t\t.to.throw('Missing \"possibleLengths\" in metadata.')\r\n\t\t_isPossiblePhoneNumber('+888 123 456 78901', oldMetadata).should.equal(true)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,4BAAnC;AACA,OAAOC,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AACA,OAAOC,WAAP,MAAwB,0CAAxB,UAA4ED,IAAI,EAAE,MAAlF;;AAEA,SAASE,qBAAT,GAA8C;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAC7CA,UAAU,CAACC,IAAX,CAAgBL,QAAhB;EACA,OAAOD,sBAAsB,CAACO,KAAvB,CAA6B,IAA7B,EAAmCF,UAAnC,CAAP;AACA;;AAEDG,QAAQ,CAAC,uBAAD,EAA0B,YAAM;EACvCC,EAAE,CAAC,kDAAD,EAAqD,YAAM;IAC5DL,qBAAqB,CAAC,mBAAD,EAAsB,IAAtB,CAArB,CAAiDM,MAAjD,CAAwDC,KAAxD,CAA8D,IAA9D;IACAP,qBAAqB,CAAC,qBAAD,EAAwB,IAAxB,CAArB,CAAmDM,MAAnD,CAA0DC,KAA1D,CAAgE,KAAhE;IACAP,qBAAqB,CAAC,yBAAD,EAA4B,IAA5B,CAArB,CAAuDM,MAAvD,CAA8DC,KAA9D,CAAoE,KAApE;IACAP,qBAAqB,CAAC,mBAAD,EAAsB;MAAEQ,cAAc,EAAE;IAAlB,CAAtB,CAArB,CAAqEF,MAArE,CAA4EC,KAA5E,CAAkF,IAAlF;IACAP,qBAAqB,CAAC,oBAAD,CAArB,CAA4CM,MAA5C,CAAmDC,KAAnD,CAAyD,IAAzD;IACAP,qBAAqB,CAAC,sBAAD,CAArB,CAA8CM,MAA9C,CAAqDC,KAArD,CAA2D,KAA3D;IACAP,qBAAqB,CAAC,qBAAD,CAArB,CAA6CM,MAA7C,CAAoDC,KAApD,CAA0D,KAA1D;IACAP,qBAAqB,CAAC,GAAD,CAArB,CAA2BM,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;EACA,CATC,CAAF;EAWAF,EAAE,CAAC,0EAAD,EAA6E,YAAM;IACpFI,MAAM,CAAC;MAAA,OAAMb,sBAAsB,CAAC,mBAAD,EAAsB,IAAtB,EAA4BG,WAA5B,CAA5B;IAAA,CAAD,CAAN,CACEW,EADF,UACW,wCADX;;IAEAd,sBAAsB,CAAC,oBAAD,EAAuBG,WAAvB,CAAtB,CAA0DO,MAA1D,CAAiEC,KAAjE,CAAuE,IAAvE;EACA,CAJC,CAAF;AAKA,CAjBO,CAAR"}