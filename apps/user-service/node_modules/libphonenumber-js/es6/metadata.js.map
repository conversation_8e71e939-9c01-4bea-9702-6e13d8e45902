{"version": 3, "file": "metadata.js", "names": ["compare", "isObject", "V2", "V3", "V4", "DEFAULT_EXT_PREFIX", "CALLING_CODE_REG_EXP", "<PERSON><PERSON><PERSON>", "metadata", "validateMetadata", "setVersion", "call", "Object", "keys", "countries", "filter", "_", "countryCode", "v1", "v2", "v3", "nonGeographic", "nonGeographical", "country", "getCountryMetadata", "undefined", "callingCode", "getCountryCodesForCallingCode", "countryCodes", "countryCallingCodes", "length", "selectNumberingPlan", "test", "hasCountry", "Error", "numberingPlan", "NumberingPlan", "hasCallingCode", "getNumberingPlanMetadata", "getCountryCodeForCallingCode", "IDDPrefix", "defaultIDDPrefix", "nationalNumberPattern", "possibleLengths", "formats", "nationalPrefixForParsing", "nationalPrefixTransformRule", "leadingDigits", "hasTypes", "type", "ext", "country_phone_code_to_countries", "country_calling_codes", "globalMetadataObject", "_getFormats", "getDefaultCountryMetadataForRegion", "map", "Format", "_getNationalPrefixFormattingRule", "_nationalPrefixForParsing", "nationalPrefix", "_getNationalPrefixIsOptionalWhenFormatting", "types", "getType", "Type", "format", "_format", "nationalPrefixFormattingRule", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "usesNationalPrefix", "FIRST_GROUP_ONLY_PREFIX_PATTERN", "join", "typeOf", "getExtPrefix", "getCountryCallingCode", "countryCallingCode", "isSupportedCountry", "hasOwnProperty", "version", "v4"], "sources": ["../source/metadata.js"], "sourcesContent": ["import compare from './tools/semver-compare.js'\r\nimport isObject from './helpers/isObject.js'\r\n\r\n// Added \"possibleLengths\" and renamed\r\n// \"country_phone_code_to_countries\" to \"country_calling_codes\".\r\nconst V2 = '1.0.18'\r\n\r\n// Added \"idd_prefix\" and \"default_idd_prefix\".\r\nconst V3 = '1.2.0'\r\n\r\n// Moved `001` country code to \"nonGeographic\" section of metadata.\r\nconst V4 = '1.7.35'\r\n\r\nconst DEFAULT_EXT_PREFIX = ' ext. '\r\n\r\nconst CALLING_CODE_REG_EXP = /^\\d+$/\r\n\r\n/**\r\n * See: https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md\r\n */\r\nexport default class Metadata {\r\n\tconstructor(metadata) {\r\n\t\tvalidateMetadata(metadata)\r\n\t\tthis.metadata = metadata\r\n\t\tsetVersion.call(this, metadata)\r\n\t}\r\n\r\n\tgetCountries() {\r\n\t\treturn Object.keys(this.metadata.countries).filter(_ => _ !== '001')\r\n\t}\r\n\r\n\tgetCountryMetadata(countryCode) {\r\n\t\treturn this.metadata.countries[countryCode]\r\n\t}\r\n\r\n\tnonGeographic() {\r\n\t\tif (this.v1 || this.v2 || this.v3) return\r\n\t\t// `nonGeographical` was a typo.\r\n\t\t// It's present in metadata generated from `1.7.35` to `1.7.37`.\r\n\t\t// The test case could be found by searching for \"nonGeographical\".\r\n\t\treturn this.metadata.nonGeographic || this.metadata.nonGeographical\r\n\t}\r\n\r\n\thasCountry(country) {\r\n\t\treturn this.getCountryMetadata(country) !== undefined\r\n\t}\r\n\r\n\thasCallingCode(callingCode) {\r\n\t\tif (this.getCountryCodesForCallingCode(callingCode)) {\r\n\t\t\treturn true\r\n\t\t}\r\n\t\tif (this.nonGeographic()) {\r\n\t\t\tif (this.nonGeographic()[callingCode]) {\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// A hacky workaround for old custom metadata (generated before V4).\r\n\t\t\tconst countryCodes = this.countryCallingCodes()[callingCode]\r\n\t\t\tif (countryCodes && countryCodes.length === 1 && countryCodes[0] === '001') {\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tisNonGeographicCallingCode(callingCode) {\r\n\t\tif (this.nonGeographic()) {\r\n\t\t\treturn this.nonGeographic()[callingCode] ? true : false\r\n\t\t} else {\r\n\t\t\treturn this.getCountryCodesForCallingCode(callingCode) ? false : true\r\n\t\t}\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tcountry(countryCode) {\r\n\t\treturn this.selectNumberingPlan(countryCode)\r\n\t}\r\n\r\n\tselectNumberingPlan(countryCode, callingCode) {\r\n\t\t// Supports just passing `callingCode` as the first argument.\r\n\t\tif (countryCode && CALLING_CODE_REG_EXP.test(countryCode)) {\r\n\t\t\tcallingCode = countryCode\r\n\t\t\tcountryCode = null\r\n\t\t}\r\n\t\tif (countryCode && countryCode !== '001') {\r\n\t\t\tif (!this.hasCountry(countryCode)) {\r\n\t\t\t\tthrow new Error(`Unknown country: ${countryCode}`)\r\n\t\t\t}\r\n\t\t\tthis.numberingPlan = new NumberingPlan(this.getCountryMetadata(countryCode), this)\r\n\t\t} else if (callingCode) {\r\n\t\t\tif (!this.hasCallingCode(callingCode)) {\r\n\t\t\t\tthrow new Error(`Unknown calling code: ${callingCode}`)\r\n\t\t\t}\r\n\t\t\tthis.numberingPlan = new NumberingPlan(this.getNumberingPlanMetadata(callingCode), this)\r\n\t\t} else {\r\n\t\t\tthis.numberingPlan = undefined\r\n\t\t}\r\n\t\treturn this\r\n\t}\r\n\r\n\tgetCountryCodesForCallingCode(callingCode) {\r\n\t\tconst countryCodes = this.countryCallingCodes()[callingCode]\r\n\t\tif (countryCodes) {\r\n\t\t\t// Metadata before V4 included \"non-geographic entity\" calling codes\r\n\t\t\t// inside `country_calling_codes` (for example, `\"881\":[\"001\"]`).\r\n\t\t\t// Now the semantics of `country_calling_codes` has changed:\r\n\t\t\t// it's specifically for \"countries\" now.\r\n\t\t\t// Older versions of custom metadata will simply skip parsing\r\n\t\t\t// \"non-geographic entity\" phone numbers with new versions\r\n\t\t\t// of this library: it's not considered a bug,\r\n\t\t\t// because such numbers are extremely rare,\r\n\t\t\t// and developers extremely rarely use custom metadata.\r\n\t\t\tif (countryCodes.length === 1 && countryCodes[0].length === 3) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\treturn countryCodes\r\n\t\t}\r\n\t}\r\n\r\n\tgetCountryCodeForCallingCode(callingCode) {\r\n\t\tconst countryCodes = this.getCountryCodesForCallingCode(callingCode)\r\n\t\tif (countryCodes) {\r\n\t\t\treturn countryCodes[0]\r\n\t\t}\r\n\t}\r\n\r\n\tgetNumberingPlanMetadata(callingCode) {\r\n\t\tconst countryCode = this.getCountryCodeForCallingCode(callingCode)\r\n\t\tif (countryCode) {\r\n\t\t\treturn this.getCountryMetadata(countryCode)\r\n\t\t}\r\n\t\tif (this.nonGeographic()) {\r\n\t\t\tconst metadata = this.nonGeographic()[callingCode]\r\n\t\t\tif (metadata) {\r\n\t\t\t\treturn metadata\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// A hacky workaround for old custom metadata (generated before V4).\r\n\t\t\t// In that metadata, there was no concept of \"non-geographic\" metadata\r\n\t\t\t// so metadata for `001` country code was stored along with other countries.\r\n\t\t\t// The test case can be found by searching for:\r\n\t\t\t// \"should work around `nonGeographic` metadata not existing\".\r\n\t\t\tconst countryCodes = this.countryCallingCodes()[callingCode]\r\n\t\t\tif (countryCodes && countryCodes.length === 1 && countryCodes[0] === '001') {\r\n\t\t\t\treturn this.metadata.countries['001']\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tcountryCallingCode() {\r\n\t\treturn this.numberingPlan.callingCode()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tIDDPrefix() {\r\n\t\treturn this.numberingPlan.IDDPrefix()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tdefaultIDDPrefix() {\r\n\t\treturn this.numberingPlan.defaultIDDPrefix()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tnationalNumberPattern() {\r\n\t\treturn this.numberingPlan.nationalNumberPattern()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tpossibleLengths() {\r\n\t\treturn this.numberingPlan.possibleLengths()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tformats() {\r\n\t\treturn this.numberingPlan.formats()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tnationalPrefixForParsing() {\r\n\t\treturn this.numberingPlan.nationalPrefixForParsing()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tnationalPrefixTransformRule() {\r\n\t\treturn this.numberingPlan.nationalPrefixTransformRule()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tleadingDigits() {\r\n\t\treturn this.numberingPlan.leadingDigits()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\thasTypes() {\r\n\t\treturn this.numberingPlan.hasTypes()\r\n\t}\r\n\r\n\t// Deprecated.\r\n\ttype(type) {\r\n\t\treturn this.numberingPlan.type(type)\r\n\t}\r\n\r\n\t// Deprecated.\r\n\text() {\r\n\t\treturn this.numberingPlan.ext()\r\n\t}\r\n\r\n\tcountryCallingCodes() {\r\n\t\tif (this.v1) return this.metadata.country_phone_code_to_countries\r\n\t\treturn this.metadata.country_calling_codes\r\n\t}\r\n\r\n\t// Deprecated.\r\n\tchooseCountryByCountryCallingCode(callingCode) {\r\n\t\treturn this.selectNumberingPlan(callingCode)\r\n\t}\r\n\r\n\thasSelectedNumberingPlan() {\r\n\t\treturn this.numberingPlan !== undefined\r\n\t}\r\n}\r\n\r\nclass NumberingPlan {\r\n\tconstructor(metadata, globalMetadataObject) {\r\n\t\tthis.globalMetadataObject = globalMetadataObject\r\n\t\tthis.metadata = metadata\r\n\t\tsetVersion.call(this, globalMetadataObject.metadata)\r\n\t}\r\n\r\n\tcallingCode() {\r\n\t\treturn this.metadata[0]\r\n\t}\r\n\r\n\t// Formatting information for regions which share\r\n\t// a country calling code is contained by only one region\r\n\t// for performance reasons. For example, for NANPA region\r\n\t// (\"North American Numbering Plan Administration\",\r\n\t//  which includes USA, Canada, Cayman Islands, Bahamas, etc)\r\n\t// it will be contained in the metadata for `US`.\r\n\tgetDefaultCountryMetadataForRegion() {\r\n\t\treturn this.globalMetadataObject.getNumberingPlanMetadata(this.callingCode())\r\n\t}\r\n\r\n\t// Is always present.\r\n\tIDDPrefix() {\r\n\t\tif (this.v1 || this.v2) return\r\n\t\treturn this.metadata[1]\r\n\t}\r\n\r\n\t// Is only present when a country supports multiple IDD prefixes.\r\n\tdefaultIDDPrefix() {\r\n\t\tif (this.v1 || this.v2) return\r\n\t\treturn this.metadata[12]\r\n\t}\r\n\r\n\tnationalNumberPattern() {\r\n\t\tif (this.v1 || this.v2) return this.metadata[1]\r\n\t\treturn this.metadata[2]\r\n\t}\r\n\r\n\t// \"possible length\" data is always present in Google's metadata.\r\n\tpossibleLengths() {\r\n\t\tif (this.v1) return\r\n\t\treturn this.metadata[this.v2 ? 2 : 3]\r\n\t}\r\n\r\n\t_getFormats(metadata) {\r\n\t\treturn metadata[this.v1 ? 2 : this.v2 ? 3 : 4]\r\n\t}\r\n\r\n\t// For countries of the same region (e.g. NANPA)\r\n\t// formats are all stored in the \"main\" country for that region.\r\n\t// E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\r\n\tformats() {\r\n\t\tconst formats = this._getFormats(this.metadata) || this._getFormats(this.getDefaultCountryMetadataForRegion()) || []\r\n\t\treturn formats.map(_ => new Format(_, this))\r\n\t}\r\n\r\n\tnationalPrefix() {\r\n\t\treturn this.metadata[this.v1 ? 3 : this.v2 ? 4 : 5]\r\n\t}\r\n\r\n\t_getNationalPrefixFormattingRule(metadata) {\r\n\t\treturn metadata[this.v1 ? 4 : this.v2 ? 5 : 6]\r\n\t}\r\n\r\n\t// For countries of the same region (e.g. NANPA)\r\n\t// national prefix formatting rule is stored in the \"main\" country for that region.\r\n\t// E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\r\n\tnationalPrefixFormattingRule() {\r\n\t\treturn this._getNationalPrefixFormattingRule(this.metadata) || this._getNationalPrefixFormattingRule(this.getDefaultCountryMetadataForRegion())\r\n\t}\r\n\r\n\t_nationalPrefixForParsing() {\r\n\t\treturn this.metadata[this.v1 ? 5 : this.v2 ? 6 : 7]\r\n\t}\r\n\r\n\tnationalPrefixForParsing() {\r\n\t\t// If `national_prefix_for_parsing` is not set explicitly,\r\n\t\t// then infer it from `national_prefix` (if any)\r\n\t\treturn this._nationalPrefixForParsing() || this.nationalPrefix()\r\n\t}\r\n\r\n\tnationalPrefixTransformRule() {\r\n\t\treturn this.metadata[this.v1 ? 6 : this.v2 ? 7 : 8]\r\n\t}\r\n\r\n\t_getNationalPrefixIsOptionalWhenFormatting() {\r\n\t\treturn !!this.metadata[this.v1 ? 7 : this.v2 ? 8 : 9]\r\n\t}\r\n\r\n\t// For countries of the same region (e.g. NANPA)\r\n\t// \"national prefix is optional when formatting\" flag is\r\n\t// stored in the \"main\" country for that region.\r\n\t// E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\r\n\tnationalPrefixIsOptionalWhenFormattingInNationalFormat() {\r\n\t\treturn this._getNationalPrefixIsOptionalWhenFormatting(this.metadata) ||\r\n\t\t\tthis._getNationalPrefixIsOptionalWhenFormatting(this.getDefaultCountryMetadataForRegion())\r\n\t}\r\n\r\n\tleadingDigits() {\r\n\t\treturn this.metadata[this.v1 ? 8 : this.v2 ? 9 : 10]\r\n\t}\r\n\r\n\ttypes() {\r\n\t\treturn this.metadata[this.v1 ? 9 : this.v2 ? 10 : 11]\r\n\t}\r\n\r\n\thasTypes() {\r\n\t\t// Versions 1.2.0 - 1.2.4: can be `[]`.\r\n\t\t/* istanbul ignore next */\r\n\t\tif (this.types() && this.types().length === 0) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\t// Versions <= 1.2.4: can be `undefined`.\r\n\t\t// Version >= 1.2.5: can be `0`.\r\n\t\treturn !!this.types()\r\n\t}\r\n\r\n\ttype(type) {\r\n\t\tif (this.hasTypes() && getType(this.types(), type)) {\r\n\t\t\treturn new Type(getType(this.types(), type), this)\r\n\t\t}\r\n\t}\r\n\r\n\text() {\r\n\t\tif (this.v1 || this.v2) return DEFAULT_EXT_PREFIX\r\n\t\treturn this.metadata[13] || DEFAULT_EXT_PREFIX\r\n\t}\r\n}\r\n\r\nclass Format {\r\n\tconstructor(format, metadata) {\r\n\t\tthis._format = format\r\n\t\tthis.metadata = metadata\r\n\t}\r\n\r\n\tpattern() {\r\n\t\treturn this._format[0]\r\n\t}\r\n\r\n\tformat() {\r\n\t\treturn this._format[1]\r\n\t}\r\n\r\n\tleadingDigitsPatterns() {\r\n\t\treturn this._format[2] || []\r\n\t}\r\n\r\n\tnationalPrefixFormattingRule() {\r\n\t\treturn this._format[3] || this.metadata.nationalPrefixFormattingRule()\r\n\t}\r\n\r\n\tnationalPrefixIsOptionalWhenFormattingInNationalFormat() {\r\n\t\treturn !!this._format[4] || this.metadata.nationalPrefixIsOptionalWhenFormattingInNationalFormat()\r\n\t}\r\n\r\n\tnationalPrefixIsMandatoryWhenFormattingInNationalFormat() {\r\n\t\t// National prefix is omitted if there's no national prefix formatting rule\r\n\t\t// set for this country, or when the national prefix formatting rule\r\n\t\t// contains no national prefix itself, or when this rule is set but\r\n\t\t// national prefix is optional for this phone number format\r\n\t\t// (and it is not enforced explicitly)\r\n\t\treturn this.usesNationalPrefix() && !this.nationalPrefixIsOptionalWhenFormattingInNationalFormat()\r\n\t}\r\n\r\n\t// Checks whether national prefix formatting rule contains national prefix.\r\n\tusesNationalPrefix() {\r\n\t\treturn this.nationalPrefixFormattingRule() &&\r\n\t\t\t// Check that national prefix formatting rule is not a \"dummy\" one.\r\n\t\t\t!FIRST_GROUP_ONLY_PREFIX_PATTERN.test(this.nationalPrefixFormattingRule())\r\n\t\t\t// In compressed metadata, `this.nationalPrefixFormattingRule()` is `0`\r\n\t\t\t// when `national_prefix_formatting_rule` is not present.\r\n\t\t\t// So, `true` or `false` are returned explicitly here, so that\r\n\t\t\t// `0` number isn't returned.\r\n\t\t\t? true\r\n\t\t\t: false\r\n\t}\r\n\r\n\tinternationalFormat() {\r\n\t\treturn this._format[5] || this.format()\r\n\t}\r\n}\r\n\r\n/**\r\n * A pattern that is used to determine if the national prefix formatting rule\r\n * has the first group only, i.e., does not start with the national prefix.\r\n * Note that the pattern explicitly allows for unbalanced parentheses.\r\n */\r\nconst FIRST_GROUP_ONLY_PREFIX_PATTERN = /^\\(?\\$1\\)?$/\r\n\r\nclass Type {\r\n\tconstructor(type, metadata) {\r\n\t\tthis.type = type\r\n\t\tthis.metadata = metadata\r\n\t}\r\n\r\n\tpattern() {\r\n\t\tif (this.metadata.v1) return this.type\r\n\t\treturn this.type[0]\r\n\t}\r\n\r\n\tpossibleLengths() {\r\n\t\tif (this.metadata.v1) return\r\n\t\treturn this.type[1] || this.metadata.possibleLengths()\r\n\t}\r\n}\r\n\r\nfunction getType(types, type) {\r\n\tswitch (type) {\r\n\t\tcase 'FIXED_LINE':\r\n\t\t\treturn types[0]\r\n\t\tcase 'MOBILE':\r\n\t\t\treturn types[1]\r\n\t\tcase 'TOLL_FREE':\r\n\t\t\treturn types[2]\r\n\t\tcase 'PREMIUM_RATE':\r\n\t\t\treturn types[3]\r\n\t\tcase 'PERSONAL_NUMBER':\r\n\t\t\treturn types[4]\r\n\t\tcase 'VOICEMAIL':\r\n\t\t\treturn types[5]\r\n\t\tcase 'UAN':\r\n\t\t\treturn types[6]\r\n\t\tcase 'PAGER':\r\n\t\t\treturn types[7]\r\n\t\tcase 'VOIP':\r\n\t\t\treturn types[8]\r\n\t\tcase 'SHARED_COST':\r\n\t\t\treturn types[9]\r\n\t}\r\n}\r\n\r\nexport function validateMetadata(metadata) {\r\n\tif (!metadata) {\r\n\t\tthrow new Error('[libphonenumber-js] `metadata` argument not passed. Check your arguments.')\r\n\t}\r\n\r\n\t// `country_phone_code_to_countries` was renamed to `country_calling_codes` in `1.0.18`.\r\n\t// For that reason, it's not used in this detection algorithm.\r\n\t// Instead, it detects by `countries: {}` property existence.\r\n\tif (!isObject(metadata) || !isObject(metadata.countries)) {\r\n\t\tthrow new Error(`[libphonenumber-js] \\`metadata\\` argument was passed but it's not a valid metadata. Must be an object having \\`.countries\\` child object property. Got ${isObject(metadata) ? 'an object of shape: { ' + Object.keys(metadata).join(', ') + ' }' : 'a ' + typeOf(metadata) + ': ' + metadata}.`)\r\n\t}\r\n}\r\n\r\n// Babel transforms `typeof` into some \"branches\"\r\n// so istanbul will show this as \"branch not covered\".\r\n/* istanbul ignore next */\r\nconst typeOf = _ => typeof _\r\n\r\n/**\r\n * Returns extension prefix for a country.\r\n * @param  {string} country\r\n * @param  {object} metadata\r\n * @return {string?}\r\n * @example\r\n * // Returns \" ext. \"\r\n * getExtPrefix(\"US\")\r\n */\r\nexport function getExtPrefix(country, metadata) {\r\n\tmetadata = new Metadata(metadata)\r\n\tif (metadata.hasCountry(country)) {\r\n\t\treturn metadata.country(country).ext()\r\n\t}\r\n\treturn DEFAULT_EXT_PREFIX\r\n}\r\n\r\n/**\r\n * Returns \"country calling code\" for a country.\r\n * Throws an error if the country doesn't exist or isn't supported by this library.\r\n * @param  {string} country\r\n * @param  {object} metadata\r\n * @return {string}\r\n * @example\r\n * // Returns \"44\"\r\n * getCountryCallingCode(\"GB\")\r\n */\r\nexport function getCountryCallingCode(country, metadata) {\r\n\tmetadata = new Metadata(metadata)\r\n\tif (metadata.hasCountry(country)) {\r\n\t\treturn metadata.country(country).countryCallingCode()\r\n\t}\r\n\tthrow new Error(`Unknown country: ${country}`)\r\n}\r\n\r\nexport function isSupportedCountry(country, metadata) {\r\n\t// metadata = new Metadata(metadata)\r\n\t// return metadata.hasCountry(country)\r\n\treturn metadata.countries.hasOwnProperty(country)\r\n}\r\n\r\nfunction setVersion(metadata) {\r\n\tconst { version } = metadata\r\n\tif (typeof version === 'number') {\r\n\t\tthis.v1 = version === 1\r\n\t\tthis.v2 = version === 2\r\n\t\tthis.v3 = version === 3\r\n\t\tthis.v4 = version === 4\r\n\t} else {\r\n\t\tif (!version) {\r\n\t\t\tthis.v1 = true\r\n\t\t} else if (compare(version, V3) === -1) {\r\n\t\t\tthis.v2 = true\r\n\t\t} else if (compare(version, V4) === -1) {\r\n\t\t\tthis.v3 = true\r\n\t\t} else {\r\n\t\t\tthis.v4 = true\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// const ISO_COUNTRY_CODE = /^[A-Z]{2}$/\r\n// function isCountryCode(countryCode) {\r\n// \treturn ISO_COUNTRY_CODE.test(countryCodeOrCountryCallingCode)\r\n// }"], "mappings": ";;;;;;;;AAAA,OAAOA,OAAP,MAAoB,2BAApB;AACA,OAAOC,QAAP,MAAqB,uBAArB,C,CAEA;AACA;;AACA,IAAMC,EAAE,GAAG,QAAX,C,CAEA;;AACA,IAAMC,EAAE,GAAG,OAAX,C,CAEA;;AACA,IAAMC,EAAE,GAAG,QAAX;AAEA,IAAMC,kBAAkB,GAAG,QAA3B;AAEA,IAAMC,oBAAoB,GAAG,OAA7B;AAEA;AACA;AACA;;IACqBC,Q;EACpB,kBAAYC,QAAZ,EAAsB;IAAA;;IACrBC,gBAAgB,CAACD,QAAD,CAAhB;IACA,KAAKA,QAAL,GAAgBA,QAAhB;IACAE,UAAU,CAACC,IAAX,CAAgB,IAAhB,EAAsBH,QAAtB;EACA;;;;WAED,wBAAe;MACd,OAAOI,MAAM,CAACC,IAAP,CAAY,KAAKL,QAAL,CAAcM,SAA1B,EAAqCC,MAArC,CAA4C,UAAAC,CAAC;QAAA,OAAIA,CAAC,KAAK,KAAV;MAAA,CAA7C,CAAP;IACA;;;WAED,4BAAmBC,WAAnB,EAAgC;MAC/B,OAAO,KAAKT,QAAL,CAAcM,SAAd,CAAwBG,WAAxB,CAAP;IACA;;;WAED,yBAAgB;MACf,IAAI,KAAKC,EAAL,IAAW,KAAKC,EAAhB,IAAsB,KAAKC,EAA/B,EAAmC,OADpB,CAEf;MACA;MACA;;MACA,OAAO,KAAKZ,QAAL,CAAca,aAAd,IAA+B,KAAKb,QAAL,CAAcc,eAApD;IACA;;;WAED,oBAAWC,OAAX,EAAoB;MACnB,OAAO,KAAKC,kBAAL,CAAwBD,OAAxB,MAAqCE,SAA5C;IACA;;;WAED,wBAAeC,WAAf,EAA4B;MAC3B,IAAI,KAAKC,6BAAL,CAAmCD,WAAnC,CAAJ,EAAqD;QACpD,OAAO,IAAP;MACA;;MACD,IAAI,KAAKL,aAAL,EAAJ,EAA0B;QACzB,IAAI,KAAKA,aAAL,GAAqBK,WAArB,CAAJ,EAAuC;UACtC,OAAO,IAAP;QACA;MACD,CAJD,MAIO;QACN;QACA,IAAME,YAAY,GAAG,KAAKC,mBAAL,GAA2BH,WAA3B,CAArB;;QACA,IAAIE,YAAY,IAAIA,YAAY,CAACE,MAAb,KAAwB,CAAxC,IAA6CF,YAAY,CAAC,CAAD,CAAZ,KAAoB,KAArE,EAA4E;UAC3E,OAAO,IAAP;QACA;MACD;IACD;;;WAED,oCAA2BF,WAA3B,EAAwC;MACvC,IAAI,KAAKL,aAAL,EAAJ,EAA0B;QACzB,OAAO,KAAKA,aAAL,GAAqBK,WAArB,IAAoC,IAApC,GAA2C,KAAlD;MACA,CAFD,MAEO;QACN,OAAO,KAAKC,6BAAL,CAAmCD,WAAnC,IAAkD,KAAlD,GAA0D,IAAjE;MACA;IACD,C,CAED;;;;WACA,iBAAQT,WAAR,EAAqB;MACpB,OAAO,KAAKc,mBAAL,CAAyBd,WAAzB,CAAP;IACA;;;WAED,6BAAoBA,WAApB,EAAiCS,WAAjC,EAA8C;MAC7C;MACA,IAAIT,WAAW,IAAIX,oBAAoB,CAAC0B,IAArB,CAA0Bf,WAA1B,CAAnB,EAA2D;QAC1DS,WAAW,GAAGT,WAAd;QACAA,WAAW,GAAG,IAAd;MACA;;MACD,IAAIA,WAAW,IAAIA,WAAW,KAAK,KAAnC,EAA0C;QACzC,IAAI,CAAC,KAAKgB,UAAL,CAAgBhB,WAAhB,CAAL,EAAmC;UAClC,MAAM,IAAIiB,KAAJ,4BAA8BjB,WAA9B,EAAN;QACA;;QACD,KAAKkB,aAAL,GAAqB,IAAIC,aAAJ,CAAkB,KAAKZ,kBAAL,CAAwBP,WAAxB,CAAlB,EAAwD,IAAxD,CAArB;MACA,CALD,MAKO,IAAIS,WAAJ,EAAiB;QACvB,IAAI,CAAC,KAAKW,cAAL,CAAoBX,WAApB,CAAL,EAAuC;UACtC,MAAM,IAAIQ,KAAJ,iCAAmCR,WAAnC,EAAN;QACA;;QACD,KAAKS,aAAL,GAAqB,IAAIC,aAAJ,CAAkB,KAAKE,wBAAL,CAA8BZ,WAA9B,CAAlB,EAA8D,IAA9D,CAArB;MACA,CALM,MAKA;QACN,KAAKS,aAAL,GAAqBV,SAArB;MACA;;MACD,OAAO,IAAP;IACA;;;WAED,uCAA8BC,WAA9B,EAA2C;MAC1C,IAAME,YAAY,GAAG,KAAKC,mBAAL,GAA2BH,WAA3B,CAArB;;MACA,IAAIE,YAAJ,EAAkB;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,YAAY,CAACE,MAAb,KAAwB,CAAxB,IAA6BF,YAAY,CAAC,CAAD,CAAZ,CAAgBE,MAAhB,KAA2B,CAA5D,EAA+D;UAC9D;QACA;;QACD,OAAOF,YAAP;MACA;IACD;;;WAED,sCAA6BF,WAA7B,EAA0C;MACzC,IAAME,YAAY,GAAG,KAAKD,6BAAL,CAAmCD,WAAnC,CAArB;;MACA,IAAIE,YAAJ,EAAkB;QACjB,OAAOA,YAAY,CAAC,CAAD,CAAnB;MACA;IACD;;;WAED,kCAAyBF,WAAzB,EAAsC;MACrC,IAAMT,WAAW,GAAG,KAAKsB,4BAAL,CAAkCb,WAAlC,CAApB;;MACA,IAAIT,WAAJ,EAAiB;QAChB,OAAO,KAAKO,kBAAL,CAAwBP,WAAxB,CAAP;MACA;;MACD,IAAI,KAAKI,aAAL,EAAJ,EAA0B;QACzB,IAAMb,QAAQ,GAAG,KAAKa,aAAL,GAAqBK,WAArB,CAAjB;;QACA,IAAIlB,QAAJ,EAAc;UACb,OAAOA,QAAP;QACA;MACD,CALD,MAKO;QACN;QACA;QACA;QACA;QACA;QACA,IAAMoB,YAAY,GAAG,KAAKC,mBAAL,GAA2BH,WAA3B,CAArB;;QACA,IAAIE,YAAY,IAAIA,YAAY,CAACE,MAAb,KAAwB,CAAxC,IAA6CF,YAAY,CAAC,CAAD,CAAZ,KAAoB,KAArE,EAA4E;UAC3E,OAAO,KAAKpB,QAAL,CAAcM,SAAd,CAAwB,KAAxB,CAAP;QACA;MACD;IACD,C,CAED;;;;WACA,8BAAqB;MACpB,OAAO,KAAKqB,aAAL,CAAmBT,WAAnB,EAAP;IACA,C,CAED;;;;WACA,qBAAY;MACX,OAAO,KAAKS,aAAL,CAAmBK,SAAnB,EAAP;IACA,C,CAED;;;;WACA,4BAAmB;MAClB,OAAO,KAAKL,aAAL,CAAmBM,gBAAnB,EAAP;IACA,C,CAED;;;;WACA,iCAAwB;MACvB,OAAO,KAAKN,aAAL,CAAmBO,qBAAnB,EAAP;IACA,C,CAED;;;;WACA,2BAAkB;MACjB,OAAO,KAAKP,aAAL,CAAmBQ,eAAnB,EAAP;IACA,C,CAED;;;;WACA,mBAAU;MACT,OAAO,KAAKR,aAAL,CAAmBS,OAAnB,EAAP;IACA,C,CAED;;;;WACA,oCAA2B;MAC1B,OAAO,KAAKT,aAAL,CAAmBU,wBAAnB,EAAP;IACA,C,CAED;;;;WACA,uCAA8B;MAC7B,OAAO,KAAKV,aAAL,CAAmBW,2BAAnB,EAAP;IACA,C,CAED;;;;WACA,yBAAgB;MACf,OAAO,KAAKX,aAAL,CAAmBY,aAAnB,EAAP;IACA,C,CAED;;;;WACA,oBAAW;MACV,OAAO,KAAKZ,aAAL,CAAmBa,QAAnB,EAAP;IACA,C,CAED;;;;WACA,cAAKC,KAAL,EAAW;MACV,OAAO,KAAKd,aAAL,CAAmBc,IAAnB,CAAwBA,KAAxB,CAAP;IACA,C,CAED;;;;WACA,eAAM;MACL,OAAO,KAAKd,aAAL,CAAmBe,GAAnB,EAAP;IACA;;;WAED,+BAAsB;MACrB,IAAI,KAAKhC,EAAT,EAAa,OAAO,KAAKV,QAAL,CAAc2C,+BAArB;MACb,OAAO,KAAK3C,QAAL,CAAc4C,qBAArB;IACA,C,CAED;;;;WACA,2CAAkC1B,WAAlC,EAA+C;MAC9C,OAAO,KAAKK,mBAAL,CAAyBL,WAAzB,CAAP;IACA;;;WAED,oCAA2B;MAC1B,OAAO,KAAKS,aAAL,KAAuBV,SAA9B;IACA;;;;;;SAxMmBlB,Q;;IA2Mf6B,a;EACL,uBAAY5B,QAAZ,EAAsB6C,oBAAtB,EAA4C;IAAA;;IAC3C,KAAKA,oBAAL,GAA4BA,oBAA5B;IACA,KAAK7C,QAAL,GAAgBA,QAAhB;IACAE,UAAU,CAACC,IAAX,CAAgB,IAAhB,EAAsB0C,oBAAoB,CAAC7C,QAA3C;EACA;;;;WAED,uBAAc;MACb,OAAO,KAAKA,QAAL,CAAc,CAAd,CAAP;IACA,C,CAED;IACA;IACA;IACA;IACA;IACA;;;;WACA,8CAAqC;MACpC,OAAO,KAAK6C,oBAAL,CAA0Bf,wBAA1B,CAAmD,KAAKZ,WAAL,EAAnD,CAAP;IACA,C,CAED;;;;WACA,qBAAY;MACX,IAAI,KAAKR,EAAL,IAAW,KAAKC,EAApB,EAAwB;MACxB,OAAO,KAAKX,QAAL,CAAc,CAAd,CAAP;IACA,C,CAED;;;;WACA,4BAAmB;MAClB,IAAI,KAAKU,EAAL,IAAW,KAAKC,EAApB,EAAwB;MACxB,OAAO,KAAKX,QAAL,CAAc,EAAd,CAAP;IACA;;;WAED,iCAAwB;MACvB,IAAI,KAAKU,EAAL,IAAW,KAAKC,EAApB,EAAwB,OAAO,KAAKX,QAAL,CAAc,CAAd,CAAP;MACxB,OAAO,KAAKA,QAAL,CAAc,CAAd,CAAP;IACA,C,CAED;;;;WACA,2BAAkB;MACjB,IAAI,KAAKU,EAAT,EAAa;MACb,OAAO,KAAKV,QAAL,CAAc,KAAKW,EAAL,GAAU,CAAV,GAAc,CAA5B,CAAP;IACA;;;WAED,qBAAYX,QAAZ,EAAsB;MACrB,OAAOA,QAAQ,CAAC,KAAKU,EAAL,GAAU,CAAV,GAAc,KAAKC,EAAL,GAAU,CAAV,GAAc,CAA7B,CAAf;IACA,C,CAED;IACA;IACA;;;;WACA,mBAAU;MAAA;;MACT,IAAMyB,OAAO,GAAG,KAAKU,WAAL,CAAiB,KAAK9C,QAAtB,KAAmC,KAAK8C,WAAL,CAAiB,KAAKC,kCAAL,EAAjB,CAAnC,IAAkG,EAAlH;MACA,OAAOX,OAAO,CAACY,GAAR,CAAY,UAAAxC,CAAC;QAAA,OAAI,IAAIyC,MAAJ,CAAWzC,CAAX,EAAc,KAAd,CAAJ;MAAA,CAAb,CAAP;IACA;;;WAED,0BAAiB;MAChB,OAAO,KAAKR,QAAL,CAAc,KAAKU,EAAL,GAAU,CAAV,GAAc,KAAKC,EAAL,GAAU,CAAV,GAAc,CAA1C,CAAP;IACA;;;WAED,0CAAiCX,QAAjC,EAA2C;MAC1C,OAAOA,QAAQ,CAAC,KAAKU,EAAL,GAAU,CAAV,GAAc,KAAKC,EAAL,GAAU,CAAV,GAAc,CAA7B,CAAf;IACA,C,CAED;IACA;IACA;;;;WACA,wCAA+B;MAC9B,OAAO,KAAKuC,gCAAL,CAAsC,KAAKlD,QAA3C,KAAwD,KAAKkD,gCAAL,CAAsC,KAAKH,kCAAL,EAAtC,CAA/D;IACA;;;WAED,qCAA4B;MAC3B,OAAO,KAAK/C,QAAL,CAAc,KAAKU,EAAL,GAAU,CAAV,GAAc,KAAKC,EAAL,GAAU,CAAV,GAAc,CAA1C,CAAP;IACA;;;WAED,oCAA2B;MAC1B;MACA;MACA,OAAO,KAAKwC,yBAAL,MAAoC,KAAKC,cAAL,EAA3C;IACA;;;WAED,uCAA8B;MAC7B,OAAO,KAAKpD,QAAL,CAAc,KAAKU,EAAL,GAAU,CAAV,GAAc,KAAKC,EAAL,GAAU,CAAV,GAAc,CAA1C,CAAP;IACA;;;WAED,sDAA6C;MAC5C,OAAO,CAAC,CAAC,KAAKX,QAAL,CAAc,KAAKU,EAAL,GAAU,CAAV,GAAc,KAAKC,EAAL,GAAU,CAAV,GAAc,CAA1C,CAAT;IACA,C,CAED;IACA;IACA;IACA;;;;WACA,kEAAyD;MACxD,OAAO,KAAK0C,0CAAL,CAAgD,KAAKrD,QAArD,KACN,KAAKqD,0CAAL,CAAgD,KAAKN,kCAAL,EAAhD,CADD;IAEA;;;WAED,yBAAgB;MACf,OAAO,KAAK/C,QAAL,CAAc,KAAKU,EAAL,GAAU,CAAV,GAAc,KAAKC,EAAL,GAAU,CAAV,GAAc,EAA1C,CAAP;IACA;;;WAED,iBAAQ;MACP,OAAO,KAAKX,QAAL,CAAc,KAAKU,EAAL,GAAU,CAAV,GAAc,KAAKC,EAAL,GAAU,EAAV,GAAe,EAA3C,CAAP;IACA;;;WAED,oBAAW;MACV;;MACA;MACA,IAAI,KAAK2C,KAAL,MAAgB,KAAKA,KAAL,GAAahC,MAAb,KAAwB,CAA5C,EAA+C;QAC9C,OAAO,KAAP;MACA,CALS,CAMV;MACA;;;MACA,OAAO,CAAC,CAAC,KAAKgC,KAAL,EAAT;IACA;;;WAED,cAAKb,MAAL,EAAW;MACV,IAAI,KAAKD,QAAL,MAAmBe,OAAO,CAAC,KAAKD,KAAL,EAAD,EAAeb,MAAf,CAA9B,EAAoD;QACnD,OAAO,IAAIe,IAAJ,CAASD,OAAO,CAAC,KAAKD,KAAL,EAAD,EAAeb,MAAf,CAAhB,EAAsC,IAAtC,CAAP;MACA;IACD;;;WAED,eAAM;MACL,IAAI,KAAK/B,EAAL,IAAW,KAAKC,EAApB,EAAwB,OAAOd,kBAAP;MACxB,OAAO,KAAKG,QAAL,CAAc,EAAd,KAAqBH,kBAA5B;IACA;;;;;;IAGIoD,M;EACL,gBAAYQ,MAAZ,EAAoBzD,QAApB,EAA8B;IAAA;;IAC7B,KAAK0D,OAAL,GAAeD,MAAf;IACA,KAAKzD,QAAL,GAAgBA,QAAhB;EACA;;;;WAED,mBAAU;MACT,OAAO,KAAK0D,OAAL,CAAa,CAAb,CAAP;IACA;;;WAED,kBAAS;MACR,OAAO,KAAKA,OAAL,CAAa,CAAb,CAAP;IACA;;;WAED,iCAAwB;MACvB,OAAO,KAAKA,OAAL,CAAa,CAAb,KAAmB,EAA1B;IACA;;;WAED,wCAA+B;MAC9B,OAAO,KAAKA,OAAL,CAAa,CAAb,KAAmB,KAAK1D,QAAL,CAAc2D,4BAAd,EAA1B;IACA;;;WAED,kEAAyD;MACxD,OAAO,CAAC,CAAC,KAAKD,OAAL,CAAa,CAAb,CAAF,IAAqB,KAAK1D,QAAL,CAAc4D,sDAAd,EAA5B;IACA;;;WAED,mEAA0D;MACzD;MACA;MACA;MACA;MACA;MACA,OAAO,KAAKC,kBAAL,MAA6B,CAAC,KAAKD,sDAAL,EAArC;IACA,C,CAED;;;;WACA,8BAAqB;MACpB,OAAO,KAAKD,4BAAL,MACN;MACA,CAACG,+BAA+B,CAACtC,IAAhC,CAAqC,KAAKmC,4BAAL,EAArC,CAFK,CAGN;MACA;MACA;MACA;MANM,EAOJ,IAPI,GAQJ,KARH;IASA;;;WAED,+BAAsB;MACrB,OAAO,KAAKD,OAAL,CAAa,CAAb,KAAmB,KAAKD,MAAL,EAA1B;IACA;;;;;AAGF;AACA;AACA;AACA;AACA;;;AACA,IAAMK,+BAA+B,GAAG,aAAxC;;IAEMN,I;EACL,cAAYf,IAAZ,EAAkBzC,QAAlB,EAA4B;IAAA;;IAC3B,KAAKyC,IAAL,GAAYA,IAAZ;IACA,KAAKzC,QAAL,GAAgBA,QAAhB;EACA;;;;WAED,mBAAU;MACT,IAAI,KAAKA,QAAL,CAAcU,EAAlB,EAAsB,OAAO,KAAK+B,IAAZ;MACtB,OAAO,KAAKA,IAAL,CAAU,CAAV,CAAP;IACA;;;WAED,2BAAkB;MACjB,IAAI,KAAKzC,QAAL,CAAcU,EAAlB,EAAsB;MACtB,OAAO,KAAK+B,IAAL,CAAU,CAAV,KAAgB,KAAKzC,QAAL,CAAcmC,eAAd,EAAvB;IACA;;;;;;AAGF,SAASoB,OAAT,CAAiBD,KAAjB,EAAwBb,IAAxB,EAA8B;EAC7B,QAAQA,IAAR;IACC,KAAK,YAAL;MACC,OAAOa,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,QAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,WAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,cAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,iBAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,WAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,KAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,OAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,MAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;;IACD,KAAK,aAAL;MACC,OAAOA,KAAK,CAAC,CAAD,CAAZ;EApBF;AAsBA;;AAED,OAAO,SAASrD,gBAAT,CAA0BD,QAA1B,EAAoC;EAC1C,IAAI,CAACA,QAAL,EAAe;IACd,MAAM,IAAI0B,KAAJ,CAAU,2EAAV,CAAN;EACA,CAHyC,CAK1C;EACA;EACA;;;EACA,IAAI,CAACjC,QAAQ,CAACO,QAAD,CAAT,IAAuB,CAACP,QAAQ,CAACO,QAAQ,CAACM,SAAV,CAApC,EAA0D;IACzD,MAAM,IAAIoB,KAAJ,8JAAoKjC,QAAQ,CAACO,QAAD,CAAR,GAAqB,2BAA2BI,MAAM,CAACC,IAAP,CAAYL,QAAZ,EAAsB+D,IAAtB,CAA2B,IAA3B,CAA3B,GAA8D,IAAnF,GAA0F,OAAOC,MAAM,CAAChE,QAAD,CAAb,GAA0B,IAA1B,GAAiCA,QAA/R,OAAN;EACA;AACD,C,CAED;AACA;;AACA;;AACA,IAAMgE,MAAM,GAAG,SAATA,MAAS,CAAAxD,CAAC;EAAA,eAAWA,CAAX;AAAA,CAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,OAAO,SAASyD,YAAT,CAAsBlD,OAAtB,EAA+Bf,QAA/B,EAAyC;EAC/CA,QAAQ,GAAG,IAAID,QAAJ,CAAaC,QAAb,CAAX;;EACA,IAAIA,QAAQ,CAACyB,UAAT,CAAoBV,OAApB,CAAJ,EAAkC;IACjC,OAAOf,QAAQ,CAACe,OAAT,CAAiBA,OAAjB,EAA0B2B,GAA1B,EAAP;EACA;;EACD,OAAO7C,kBAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASqE,qBAAT,CAA+BnD,OAA/B,EAAwCf,QAAxC,EAAkD;EACxDA,QAAQ,GAAG,IAAID,QAAJ,CAAaC,QAAb,CAAX;;EACA,IAAIA,QAAQ,CAACyB,UAAT,CAAoBV,OAApB,CAAJ,EAAkC;IACjC,OAAOf,QAAQ,CAACe,OAAT,CAAiBA,OAAjB,EAA0BoD,kBAA1B,EAAP;EACA;;EACD,MAAM,IAAIzC,KAAJ,4BAA8BX,OAA9B,EAAN;AACA;AAED,OAAO,SAASqD,kBAAT,CAA4BrD,OAA5B,EAAqCf,QAArC,EAA+C;EACrD;EACA;EACA,OAAOA,QAAQ,CAACM,SAAT,CAAmB+D,cAAnB,CAAkCtD,OAAlC,CAAP;AACA;;AAED,SAASb,UAAT,CAAoBF,QAApB,EAA8B;EAC7B,IAAQsE,OAAR,GAAoBtE,QAApB,CAAQsE,OAAR;;EACA,IAAI,OAAOA,OAAP,KAAmB,QAAvB,EAAiC;IAChC,KAAK5D,EAAL,GAAU4D,OAAO,KAAK,CAAtB;IACA,KAAK3D,EAAL,GAAU2D,OAAO,KAAK,CAAtB;IACA,KAAK1D,EAAL,GAAU0D,OAAO,KAAK,CAAtB;IACA,KAAKC,EAAL,GAAUD,OAAO,KAAK,CAAtB;EACA,CALD,MAKO;IACN,IAAI,CAACA,OAAL,EAAc;MACb,KAAK5D,EAAL,GAAU,IAAV;IACA,CAFD,MAEO,IAAIlB,OAAO,CAAC8E,OAAD,EAAU3E,EAAV,CAAP,KAAyB,CAAC,CAA9B,EAAiC;MACvC,KAAKgB,EAAL,GAAU,IAAV;IACA,CAFM,MAEA,IAAInB,OAAO,CAAC8E,OAAD,EAAU1E,EAAV,CAAP,KAAyB,CAAC,CAA9B,EAAiC;MACvC,KAAKgB,EAAL,GAAU,IAAV;IACA,CAFM,MAEA;MACN,KAAK2D,EAAL,GAAU,IAAV;IACA;EACD;AACD,C,CAED;AACA;AACA;AACA"}