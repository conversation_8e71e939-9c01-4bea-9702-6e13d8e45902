import { ValidationOptions } from '../ValidationOptions';
export declare const IS_PASSPORT_NUMBER = "isPassportNumber";
/**
 * Check if the string is a valid passport number relative to a specific country code.
 * If given value is not a string, then it returns false.
 */
export declare function isPassportNumber(value: unknown, countryCode: string): boolean;
/**
 * Check if the string is a valid passport number relative to a specific country code.
 * If given value is not a string, then it returns false.
 */
export declare function IsPassportNumber(countryCode: string, validationOptions?: ValidationOptions): PropertyDecorator;
