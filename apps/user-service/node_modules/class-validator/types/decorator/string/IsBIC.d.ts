import { ValidationOptions } from '../ValidationOptions';
export declare const IS_BIC = "isBIC";
/**
 * Check if a string is a BIC (Bank Identification Code) or SWIFT code.
 * If given value is not a string, then it returns false.
 */
export declare function isBIC(value: unknown): boolean;
/**
 * Check if a string is a BIC (Bank Identification Code) or SWIFT code.
 * If given value is not a string, then it returns false.
 */
export declare function IsBIC(validationOptions?: ValidationOptions): PropertyDecorator;
