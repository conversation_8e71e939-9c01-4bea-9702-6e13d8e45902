import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_URL = "isUrl";
/**
 * Checks if the string is a url.
 * If given value is not a string, then it returns false.
 */
export declare function isURL(value: string, options?: ValidatorJS.IsURLOptions): boolean;
/**
 * Checks if the string is a url.
 * If given value is not a string, then it returns false.
 */
export declare function IsUrl(options?: ValidatorJS.IsURLOptions, validationOptions?: ValidationOptions): PropertyDecorator;
