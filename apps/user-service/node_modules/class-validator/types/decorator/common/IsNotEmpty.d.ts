import { ValidationOptions } from '../ValidationOptions';
export declare const IS_NOT_EMPTY = "isNotEmpty";
/**
 * Checks if given value is not empty (!== '', !== null, !== undefined).
 */
export declare function isNotEmpty(value: unknown): boolean;
/**
 * Checks if given value is not empty (!== '', !== null, !== undefined).
 */
export declare function IsNotEmpty(validationOptions?: ValidationOptions): PropertyDecorator;
